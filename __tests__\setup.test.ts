/**
 * Basic setup test to verify Jest configuration is working correctly
 */

describe('Jest Setup', () => {
  it('should run basic tests', () => {
    expect(true).toBe(true)
  })

  it('should handle async operations', async () => {
    const result = await Promise.resolve('test')
    expect(result).toBe('test')
  })

  it('should have access to DOM APIs', () => {
    expect(typeof window).toBe('object')
    expect(typeof document).toBe('object')
  })

  it('should have environment variables set', () => {
    expect(process.env.NEXT_PUBLIC_API_BASE_URL).toBe('https://test-api.example.com/api/')
    expect(process.env.NEXT_PUBLIC_MEDIA_PATH).toBe('https://test-media.example.com/')
  })
})
