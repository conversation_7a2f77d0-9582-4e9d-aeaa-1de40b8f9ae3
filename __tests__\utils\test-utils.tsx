import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'

// Mock contexts
const mockCommonContext = {
  setIsLoading: jest.fn(),
  screenSize: 1920,
  isLoading: false,
}

const mockAlertContext = {
  fire: jest.fn(),
}

// Create a theme for testing
const theme = createTheme()

// Mock providers
const MockCommonContextProvider = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid="common-context-provider">{children}</div>
}

const MockAlertProvider = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid="alert-provider">{children}</div>
}

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider theme={theme}>
      <MockCommonContextProvider>
        <MockAlertProvider>
          {children}
        </MockAlertProvider>
      </MockCommonContextProvider>
    </ThemeProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock hooks
export const mockUseCommonContext = () => mockCommonContext
export const mockUseAlert = () => mockAlertContext

// Test data factories
export const createMockRoute = (overrides = {}) => ({
  id: 1,
  route_name: 'Test Route',
  day: 'Monday',
  agent_id: 1,
  branch_id: 1,
  created_by: 1,
  route_customers: [],
  ...overrides,
})

export const createMockCustomer = (overrides = {}) => ({
  id: 1,
  name: 'John Doe',
  short_title: 'Mr. John',
  priority: 1,
  profile_photo: 'profile.jpg',
  location: 'Test Location',
  address: 'Test Address',
  is_new_customer: false,
  emi_type: 'monthly',
  emi_period_type: 'monthly',
  next_emi_due_date: '2024-01-15',
  star_rating: 4,
  branch_id: 1,
  ...overrides,
})

export const createMockBranch = (overrides = {}) => ({
  id: 1,
  name: 'Test Branch',
  ...overrides,
})

export const createMockAgent = (overrides = {}) => ({
  id: 1,
  name: 'Test Agent',
  ...overrides,
})

// API response mocks
export const mockApiResponse = {
  success: (data: any) => ({
    data,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {},
  }),
  error: (message = 'API Error', status = 500) => ({
    response: {
      data: { detail: message },
      status,
      statusText: 'Error',
    },
    message,
  }),
}

// Form test helpers
export const fillFormField = async (getByLabelText: any, label: string, value: string) => {
  const field = getByLabelText(label)
  await userEvent.clear(field)
  await userEvent.type(field, value)
  return field
}

export const selectAutocompleteOption = async (container: any, label: string, option: string) => {
  const autocomplete = container.querySelector(`[data-testid="${label}"]`)
  if (autocomplete) {
    const input = autocomplete.querySelector('input')
    if (input) {
      await userEvent.click(input)
      await userEvent.type(input, option)
      // Simulate selecting the option
      const optionElement = await waitFor(() => 
        container.querySelector(`[data-value="${option}"]`)
      )
      if (optionElement) {
        await userEvent.click(optionElement)
      }
    }
  }
}

// Wait utilities
export const waitForLoadingToFinish = async () => {
  await waitFor(() => {
    expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
  })
}

import { waitFor, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
