import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { mockRouteService, resetRouteServiceMocks } from '../mocks/route-service.mock'

// Mock the entire route workflow
const MockRouteWorkflow = () => {
  const [currentView, setCurrentView] = React.useState('list')
  const [routes, setRoutes] = React.useState([])
  const [selectedRoute, setSelectedRoute] = React.useState(null)

  const handleCreateRoute = async (routeData) => {
    try {
      await mockRouteService.createRoute(routeData)
      // Refresh route list
      const response = await mockRouteService.getRouteList(0, 10, '', '')
      setRoutes(response.results)
      setCurrentView('list')
    } catch (error) {
      console.error('Failed to create route:', error)
    }
  }

  const handleEditRoute = async (routeId, routeData) => {
    try {
      await mockRouteService.editRoute(routeId, routeData)
      // Refresh route details
      const response = await mockRouteService.getRouteDetails(routeId)
      setSelectedRoute(response)
    } catch (error) {
      console.error('Failed to edit route:', error)
    }
  }

  const handleDeleteRoute = async (routeId) => {
    try {
      await mockRouteService.deleteRoute(routeId)
      // Refresh route list
      const response = await mockRouteService.getRouteList(0, 10, '', '')
      setRoutes(response.results)
    } catch (error) {
      console.error('Failed to delete route:', error)
    }
  }

  const handleViewRoute = async (routeId) => {
    try {
      const response = await mockRouteService.getRouteDetails(routeId)
      setSelectedRoute(response)
      setCurrentView('details')
    } catch (error) {
      console.error('Failed to load route details:', error)
    }
  }

  React.useEffect(() => {
    // Load initial route list
    const loadRoutes = async () => {
      try {
        const response = await mockRouteService.getRouteList(0, 10, '', '')
        setRoutes(response.results)
      } catch (error) {
        console.error('Failed to load routes:', error)
      }
    }
    loadRoutes()
  }, [])

  if (currentView === 'list') {
    return (
      <div data-testid="route-list-view">
        <h1>Route List</h1>
        <button 
          onClick={() => setCurrentView('create')}
          data-testid="create-route-button"
        >
          Create New Route
        </button>
        <div data-testid="routes-container">
          {routes.map(route => (
            <div key={route.id} data-testid={`route-${route.id}`}>
              <span>{route.route_name}</span>
              <button 
                onClick={() => handleViewRoute(route.id)}
                data-testid={`view-route-${route.id}`}
              >
                View
              </button>
              <button 
                onClick={() => handleDeleteRoute(route.id)}
                data-testid={`delete-route-${route.id}`}
              >
                Delete
              </button>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (currentView === 'create') {
    return (
      <div data-testid="route-create-view">
        <h1>Create Route</h1>
        <form onSubmit={(e) => {
          e.preventDefault()
          const formData = new FormData(e.target)
          handleCreateRoute({
            route_name: formData.get('route_name'),
            day: formData.get('day'),
            agent_id: parseInt(formData.get('agent_id')),
            branch_id: parseInt(formData.get('branch_id')),
          })
        }}>
          <input name="route_name" placeholder="Route Name" required />
          <select name="day" required>
            <option value="">Select Day</option>
            <option value="Monday">Monday</option>
            <option value="Tuesday">Tuesday</option>
          </select>
          <input name="agent_id" type="number" placeholder="Agent ID" required />
          <input name="branch_id" type="number" placeholder="Branch ID" required />
          <button type="submit" data-testid="submit-create-route">Create</button>
          <button 
            type="button" 
            onClick={() => setCurrentView('list')}
            data-testid="cancel-create-route"
          >
            Cancel
          </button>
        </form>
      </div>
    )
  }

  if (currentView === 'details') {
    return (
      <div data-testid="route-details-view">
        <h1>Route Details</h1>
        {selectedRoute && (
          <div>
            <h2>{selectedRoute.route_name}</h2>
            <p>Day: {selectedRoute.day}</p>
            <div data-testid="route-customers">
              {selectedRoute.route_customers.map(customer => (
                <div key={customer.id} data-testid={`customer-${customer.id}`}>
                  <span>{customer.name}</span>
                  <span>Priority: {customer.priority}</span>
                </div>
              ))}
            </div>
            <button 
              onClick={() => setCurrentView('edit')}
              data-testid="edit-route-button"
            >
              Edit Route
            </button>
            <button 
              onClick={() => setCurrentView('list')}
              data-testid="back-to-list-button"
            >
              Back to List
            </button>
          </div>
        )}
      </div>
    )
  }

  if (currentView === 'edit') {
    return (
      <div data-testid="route-edit-view">
        <h1>Edit Route</h1>
        <form onSubmit={(e) => {
          e.preventDefault()
          const formData = new FormData(e.target)
          handleEditRoute(selectedRoute.id, {
            route_name: formData.get('route_name'),
            day: formData.get('day'),
            agent_id: parseInt(formData.get('agent_id')),
            branch_id: parseInt(formData.get('branch_id')),
            customers: selectedRoute.route_customers.map(customer => ({
              customer_id: customer.id,
              priority: customer.priority,
            })),
          })
        }}>
          <input 
            name="route_name" 
            defaultValue={selectedRoute?.route_name} 
            placeholder="Route Name" 
            required 
          />
          <select name="day" defaultValue={selectedRoute?.day} required>
            <option value="">Select Day</option>
            <option value="Monday">Monday</option>
            <option value="Tuesday">Tuesday</option>
          </select>
          <input 
            name="agent_id" 
            type="number" 
            defaultValue={selectedRoute?.agent_id} 
            placeholder="Agent ID" 
            required 
          />
          <input 
            name="branch_id" 
            type="number" 
            defaultValue={selectedRoute?.branch_id} 
            placeholder="Branch ID" 
            required 
          />
          <button type="submit" data-testid="submit-edit-route">Save Changes</button>
          <button 
            type="button" 
            onClick={() => setCurrentView('details')}
            data-testid="cancel-edit-route"
          >
            Cancel
          </button>
        </form>
      </div>
    )
  }

  return null
}

describe('Route Management Workflow Integration Tests', () => {
  beforeEach(() => {
    resetRouteServiceMocks()
    jest.clearAllMocks()
    
    // Setup default mock returns
    mockRouteService.getRouteList.mockResolvedValue({
      results: [
        { id: 1, route_name: 'Route 1', day: 'Monday', agent_id: 1, branch_id: 1 },
        { id: 2, route_name: 'Route 2', day: 'Tuesday', agent_id: 2, branch_id: 2 },
      ],
      total_pages: 1,
      total: 2,
      page: 1,
      page_size: 10,
      links: { next: '', previous: '' },
    })

    mockRouteService.getRouteDetails.mockResolvedValue({
      id: 1,
      route_name: 'Route 1',
      day: 'Monday',
      agent_id: 1,
      branch_id: 1,
      route_customers: [
        { id: 1, name: 'Customer 1', priority: 1 },
        { id: 2, name: 'Customer 2', priority: 2 },
      ],
    })

    mockRouteService.createRoute.mockResolvedValue({ success: true })
    mockRouteService.editRoute.mockResolvedValue({ success: true })
    mockRouteService.deleteRoute.mockResolvedValue({ success: true })
  })

  it('should complete full route creation workflow', async () => {
    const user = userEvent.setup()
    render(<MockRouteWorkflow />)

    // Start at route list
    await waitFor(() => {
      expect(screen.getByTestId('route-list-view')).toBeInTheDocument()
      expect(screen.getByText('Route 1')).toBeInTheDocument()
      expect(screen.getByText('Route 2')).toBeInTheDocument()
    })

    // Navigate to create route
    await user.click(screen.getByTestId('create-route-button'))
    
    await waitFor(() => {
      expect(screen.getByTestId('route-create-view')).toBeInTheDocument()
    })

    // Fill out create form
    await user.type(screen.getByPlaceholderText('Route Name'), 'New Route')
    await user.selectOptions(screen.getByDisplayValue('Select Day'), 'Monday')
    await user.type(screen.getByPlaceholderText('Agent ID'), '1')
    await user.type(screen.getByPlaceholderText('Branch ID'), '1')

    // Submit form
    await user.click(screen.getByTestId('submit-create-route'))

    // Verify API calls
    await waitFor(() => {
      expect(mockRouteService.createRoute).toHaveBeenCalledWith({
        route_name: 'New Route',
        day: 'Monday',
        agent_id: 1,
        branch_id: 1,
      })
      expect(mockRouteService.getRouteList).toHaveBeenCalledTimes(2) // Initial load + refresh
    })

    // Should return to list view
    await waitFor(() => {
      expect(screen.getByTestId('route-list-view')).toBeInTheDocument()
    })
  })

  it('should complete full route viewing and editing workflow', async () => {
    const user = userEvent.setup()
    render(<MockRouteWorkflow />)

    // Start at route list
    await waitFor(() => {
      expect(screen.getByTestId('route-list-view')).toBeInTheDocument()
    })

    // View route details
    await user.click(screen.getByTestId('view-route-1'))

    await waitFor(() => {
      expect(screen.getByTestId('route-details-view')).toBeInTheDocument()
      expect(screen.getByText('Route 1')).toBeInTheDocument()
      expect(screen.getByText('Day: Monday')).toBeInTheDocument()
      expect(screen.getByText('Customer 1')).toBeInTheDocument()
      expect(screen.getByText('Customer 2')).toBeInTheDocument()
    })

    // Navigate to edit
    await user.click(screen.getByTestId('edit-route-button'))

    await waitFor(() => {
      expect(screen.getByTestId('route-edit-view')).toBeInTheDocument()
    })

    // Edit route name
    const routeNameInput = screen.getByDisplayValue('Route 1')
    await user.clear(routeNameInput)
    await user.type(routeNameInput, 'Updated Route 1')

    // Submit changes
    await user.click(screen.getByTestId('submit-edit-route'))

    // Verify API calls
    await waitFor(() => {
      expect(mockRouteService.editRoute).toHaveBeenCalledWith(1, {
        route_name: 'Updated Route 1',
        day: 'Monday',
        agent_id: 1,
        branch_id: 1,
        customers: [
          { customer_id: 1, priority: 1 },
          { customer_id: 2, priority: 2 },
        ],
      })
    })
  })

  it('should complete route deletion workflow', async () => {
    const user = userEvent.setup()
    render(<MockRouteWorkflow />)

    // Start at route list
    await waitFor(() => {
      expect(screen.getByTestId('route-list-view')).toBeInTheDocument()
    })

    // Delete route
    await user.click(screen.getByTestId('delete-route-1'))

    // Verify API calls
    await waitFor(() => {
      expect(mockRouteService.deleteRoute).toHaveBeenCalledWith(1)
      expect(mockRouteService.getRouteList).toHaveBeenCalledTimes(2) // Initial load + refresh
    })
  })

  it('should handle navigation between views correctly', async () => {
    const user = userEvent.setup()
    render(<MockRouteWorkflow />)

    // Start at list
    await waitFor(() => {
      expect(screen.getByTestId('route-list-view')).toBeInTheDocument()
    })

    // Go to create
    await user.click(screen.getByTestId('create-route-button'))
    expect(screen.getByTestId('route-create-view')).toBeInTheDocument()

    // Cancel back to list
    await user.click(screen.getByTestId('cancel-create-route'))
    expect(screen.getByTestId('route-list-view')).toBeInTheDocument()

    // Go to details
    await user.click(screen.getByTestId('view-route-1'))
    await waitFor(() => {
      expect(screen.getByTestId('route-details-view')).toBeInTheDocument()
    })

    // Go to edit
    await user.click(screen.getByTestId('edit-route-button'))
    expect(screen.getByTestId('route-edit-view')).toBeInTheDocument()

    // Cancel back to details
    await user.click(screen.getByTestId('cancel-edit-route'))
    expect(screen.getByTestId('route-details-view')).toBeInTheDocument()

    // Back to list
    await user.click(screen.getByTestId('back-to-list-button'))
    expect(screen.getByTestId('route-list-view')).toBeInTheDocument()
  })

  it('should handle API errors gracefully', async () => {
    const user = userEvent.setup()
    
    // Mock API failure
    mockRouteService.createRoute.mockRejectedValue(new Error('API Error'))
    
    render(<MockRouteWorkflow />)

    // Navigate to create
    await user.click(screen.getByTestId('create-route-button'))
    
    // Fill and submit form
    await user.type(screen.getByPlaceholderText('Route Name'), 'Test Route')
    await user.selectOptions(screen.getByDisplayValue('Select Day'), 'Monday')
    await user.type(screen.getByPlaceholderText('Agent ID'), '1')
    await user.type(screen.getByPlaceholderText('Branch ID'), '1')
    await user.click(screen.getByTestId('submit-create-route'))

    // Should still be on create view (error handled)
    await waitFor(() => {
      expect(screen.getByTestId('route-create-view')).toBeInTheDocument()
    })
  })
})
