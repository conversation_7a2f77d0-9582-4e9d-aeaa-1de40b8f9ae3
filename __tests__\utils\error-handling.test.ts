// Mock error handling utilities
const handleApiError = (error: any) => {
  console.error('API Error:', error)
  
  if (error.response) {
    // Server responded with error status
    const status = error.response.status
    const message = error.response.data?.detail || error.response.data?.message || 'An error occurred'
    
    switch (status) {
      case 400:
        return { type: 'validation', message: `Validation Error: ${message}` }
      case 401:
        return { type: 'auth', message: 'Authentication required. Please log in.' }
      case 403:
        return { type: 'permission', message: 'You do not have permission to perform this action.' }
      case 404:
        return { type: 'notFound', message: 'The requested resource was not found.' }
      case 409:
        return { type: 'conflict', message: `Conflict: ${message}` }
      case 422:
        return { type: 'validation', message: `Validation Error: ${message}` }
      case 500:
        return { type: 'server', message: 'Internal server error. Please try again later.' }
      default:
        return { type: 'unknown', message: `Error ${status}: ${message}` }
    }
  } else if (error.request) {
    // Network error
    return { type: 'network', message: 'Network error. Please check your connection.' }
  } else {
    // Other error
    return { type: 'unknown', message: error.message || 'An unexpected error occurred.' }
  }
}

const formatErrorForUser = (errorType: string, message: string) => {
  const userFriendlyMessages = {
    validation: 'Please check your input and try again.',
    auth: 'Please log in to continue.',
    permission: 'You do not have permission for this action.',
    notFound: 'The requested item could not be found.',
    conflict: 'This action conflicts with existing data.',
    network: 'Please check your internet connection.',
    server: 'Server is temporarily unavailable. Please try again later.',
    unknown: 'Something went wrong. Please try again.',
  }
  
  return userFriendlyMessages[errorType] || message
}

const retryWithBackoff = async (
  fn: () => Promise<any>,
  maxRetries: number = 3,
  baseDelay: number = 1000
) => {
  let lastError: any
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries) {
        throw error
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}

const isRetryableError = (error: any) => {
  if (!error.response) {
    return true // Network errors are retryable
  }
  
  const status = error.response.status
  return status >= 500 || status === 408 || status === 429
}

describe('Error Handling Utilities', () => {
  describe('handleApiError', () => {
    it('should handle 400 validation errors', () => {
      const error = {
        response: {
          status: 400,
          data: { detail: 'Invalid input data' }
        }
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('validation')
      expect(result.message).toBe('Validation Error: Invalid input data')
    })
    
    it('should handle 401 authentication errors', () => {
      const error = {
        response: {
          status: 401,
          data: { detail: 'Token expired' }
        }
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('auth')
      expect(result.message).toBe('Authentication required. Please log in.')
    })
    
    it('should handle 403 permission errors', () => {
      const error = {
        response: {
          status: 403,
          data: { detail: 'Insufficient permissions' }
        }
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('permission')
      expect(result.message).toBe('You do not have permission to perform this action.')
    })
    
    it('should handle 404 not found errors', () => {
      const error = {
        response: {
          status: 404,
          data: { detail: 'Route not found' }
        }
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('notFound')
      expect(result.message).toBe('The requested resource was not found.')
    })
    
    it('should handle 409 conflict errors', () => {
      const error = {
        response: {
          status: 409,
          data: { detail: 'Route name already exists' }
        }
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('conflict')
      expect(result.message).toBe('Conflict: Route name already exists')
    })
    
    it('should handle 500 server errors', () => {
      const error = {
        response: {
          status: 500,
          data: { detail: 'Database connection failed' }
        }
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('server')
      expect(result.message).toBe('Internal server error. Please try again later.')
    })
    
    it('should handle network errors', () => {
      const error = {
        request: {},
        message: 'Network Error'
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('network')
      expect(result.message).toBe('Network error. Please check your connection.')
    })
    
    it('should handle unknown errors', () => {
      const error = {
        message: 'Something unexpected happened'
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('unknown')
      expect(result.message).toBe('Something unexpected happened')
    })
    
    it('should handle errors without detail or message', () => {
      const error = {
        response: {
          status: 400,
          data: {}
        }
      }
      
      const result = handleApiError(error)
      
      expect(result.type).toBe('validation')
      expect(result.message).toBe('Validation Error: An error occurred')
    })
  })
  
  describe('formatErrorForUser', () => {
    it('should return user-friendly messages for known error types', () => {
      expect(formatErrorForUser('validation', 'Field required')).toBe(
        'Please check your input and try again.'
      )
      expect(formatErrorForUser('auth', 'Token expired')).toBe(
        'Please log in to continue.'
      )
      expect(formatErrorForUser('network', 'Connection failed')).toBe(
        'Please check your internet connection.'
      )
    })
    
    it('should return original message for unknown error types', () => {
      expect(formatErrorForUser('custom', 'Custom error message')).toBe(
        'Custom error message'
      )
    })
  })
  
  describe('retryWithBackoff', () => {
    it('should succeed on first attempt', async () => {
      const mockFn = jest.fn().mockResolvedValue('success')
      
      const result = await retryWithBackoff(mockFn)
      
      expect(result).toBe('success')
      expect(mockFn).toHaveBeenCalledTimes(1)
    })
    
    it('should retry on failure and eventually succeed', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue('success')
      
      const result = await retryWithBackoff(mockFn)
      
      expect(result).toBe('success')
      expect(mockFn).toHaveBeenCalledTimes(3)
    })
    
    it('should throw error after max retries', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Persistent failure'))
      
      await expect(retryWithBackoff(mockFn, 2)).rejects.toThrow('Persistent failure')
      expect(mockFn).toHaveBeenCalledTimes(3) // Initial + 2 retries
    })
    
    it('should use custom retry count', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Failure'))
      
      await expect(retryWithBackoff(mockFn, 1)).rejects.toThrow('Failure')
      expect(mockFn).toHaveBeenCalledTimes(2) // Initial + 1 retry
    })
  })
  
  describe('isRetryableError', () => {
    it('should identify retryable errors', () => {
      // Network errors (no response)
      expect(isRetryableError({ request: {} })).toBe(true)
      
      // Server errors (5xx)
      expect(isRetryableError({ response: { status: 500 } })).toBe(true)
      expect(isRetryableError({ response: { status: 502 } })).toBe(true)
      expect(isRetryableError({ response: { status: 503 } })).toBe(true)
      
      // Timeout errors
      expect(isRetryableError({ response: { status: 408 } })).toBe(true)
      
      // Rate limit errors
      expect(isRetryableError({ response: { status: 429 } })).toBe(true)
    })
    
    it('should identify non-retryable errors', () => {
      // Client errors (4xx except 408 and 429)
      expect(isRetryableError({ response: { status: 400 } })).toBe(false)
      expect(isRetryableError({ response: { status: 401 } })).toBe(false)
      expect(isRetryableError({ response: { status: 403 } })).toBe(false)
      expect(isRetryableError({ response: { status: 404 } })).toBe(false)
      expect(isRetryableError({ response: { status: 422 } })).toBe(false)
      
      // Success responses (shouldn't happen in error context, but testing)
      expect(isRetryableError({ response: { status: 200 } })).toBe(false)
    })
  })
  
  describe('Error handling integration', () => {
    it('should handle complete error flow', () => {
      const apiError = {
        response: {
          status: 422,
          data: { detail: 'Route name must be unique' }
        }
      }
      
      const handledError = handleApiError(apiError)
      const userMessage = formatErrorForUser(handledError.type, handledError.message)
      
      expect(handledError.type).toBe('validation')
      expect(userMessage).toBe('Please check your input and try again.')
    })
    
    it('should determine retry strategy based on error type', () => {
      const networkError = { request: {} }
      const validationError = { response: { status: 400 } }
      const serverError = { response: { status: 500 } }
      
      expect(isRetryableError(networkError)).toBe(true)
      expect(isRetryableError(validationError)).toBe(false)
      expect(isRetryableError(serverError)).toBe(true)
    })
  })
})
