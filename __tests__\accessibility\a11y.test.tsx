import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { createMockRoute, createMockCustomer } from '../mocks/route-service.mock'

// Mock accessible components for testing
const AccessibleRouteCard = ({ route, onEdit, onDelete }) => {
  return (
    <article 
      role="article"
      aria-labelledby={`route-title-${route.id}`}
      data-testid={`route-card-${route.id}`}
    >
      <header>
        <h3 id={`route-title-${route.id}`}>{route.route_name}</h3>
        <p aria-label={`Route day: ${route.day}`}>{route.day}</p>
      </header>
      
      <div role="group" aria-label="Route actions">
        <button
          onClick={() => onEdit(route.id)}
          aria-label={`Edit route ${route.route_name}`}
          data-testid={`edit-route-${route.id}`}
        >
          <span className="material-icons" aria-hidden="true">edit</span>
          Edit
        </button>
        
        <button
          onClick={() => onDelete(route.id)}
          aria-label={`Delete route ${route.route_name}`}
          aria-describedby={`delete-warning-${route.id}`}
          data-testid={`delete-route-${route.id}`}
        >
          <span className="material-icons" aria-hidden="true">delete</span>
          Delete
        </button>
        
        <div 
          id={`delete-warning-${route.id}`} 
          className="sr-only"
        >
          Warning: This action cannot be undone
        </div>
      </div>
    </article>
  )
}

const AccessibleForm = ({ onSubmit }) => {
  const [errors, setErrors] = React.useState({})
  
  const handleSubmit = (e) => {
    e.preventDefault()
    const formData = new FormData(e.target)
    const data = Object.fromEntries(formData)
    
    // Basic validation
    const newErrors = {}
    if (!data.route_name) {
      newErrors.route_name = 'Route name is required'
    }
    if (!data.day) {
      newErrors.day = 'Day is required'
    }
    
    setErrors(newErrors)
    
    if (Object.keys(newErrors).length === 0) {
      onSubmit(data)
    }
  }
  
  return (
    <form onSubmit={handleSubmit} noValidate>
      <fieldset>
        <legend>Route Information</legend>
        
        <div className="form-group">
          <label htmlFor="route-name">
            Route Name *
          </label>
          <input
            id="route-name"
            name="route_name"
            type="text"
            required
            aria-required="true"
            aria-invalid={errors.route_name ? 'true' : 'false'}
            aria-describedby={errors.route_name ? 'route-name-error' : undefined}
            data-testid="route-name-input"
          />
          {errors.route_name && (
            <div 
              id="route-name-error" 
              role="alert" 
              className="error-message"
              data-testid="route-name-error"
            >
              {errors.route_name}
            </div>
          )}
        </div>
        
        <div className="form-group">
          <label htmlFor="route-day">
            Day *
          </label>
          <select
            id="route-day"
            name="day"
            required
            aria-required="true"
            aria-invalid={errors.day ? 'true' : 'false'}
            aria-describedby={errors.day ? 'route-day-error' : undefined}
            data-testid="route-day-select"
          >
            <option value="">Select a day</option>
            <option value="Monday">Monday</option>
            <option value="Tuesday">Tuesday</option>
            <option value="Wednesday">Wednesday</option>
            <option value="Thursday">Thursday</option>
            <option value="Friday">Friday</option>
            <option value="Saturday">Saturday</option>
            <option value="Sunday">Sunday</option>
          </select>
          {errors.day && (
            <div 
              id="route-day-error" 
              role="alert" 
              className="error-message"
              data-testid="route-day-error"
            >
              {errors.day}
            </div>
          )}
        </div>
      </fieldset>
      
      <div className="form-actions">
        <button 
          type="submit"
          data-testid="submit-button"
        >
          Create Route
        </button>
        <button 
          type="button"
          data-testid="cancel-button"
        >
          Cancel
        </button>
      </div>
    </form>
  )
}

const AccessibleDataTable = ({ routes, onSort }) => {
  const [sortField, setSortField] = React.useState('')
  const [sortDirection, setSortDirection] = React.useState('asc')
  
  const handleSort = (field) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc'
    setSortField(field)
    setSortDirection(newDirection)
    onSort(field, newDirection)
  }
  
  return (
    <div role="region" aria-label="Routes table">
      <table role="table" aria-label="List of routes">
        <caption className="sr-only">
          Routes table with {routes.length} routes. Use arrow keys to navigate.
        </caption>
        
        <thead>
          <tr role="row">
            <th 
              role="columnheader"
              aria-sort={sortField === 'route_name' ? sortDirection : 'none'}
              tabIndex={0}
              onClick={() => handleSort('route_name')}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  handleSort('route_name')
                }
              }}
              data-testid="sort-route-name"
            >
              Route Name
              <span className="sort-indicator" aria-hidden="true">
                {sortField === 'route_name' ? (sortDirection === 'asc' ? '↑' : '↓') : '↕'}
              </span>
            </th>
            
            <th 
              role="columnheader"
              aria-sort={sortField === 'day' ? sortDirection : 'none'}
              tabIndex={0}
              onClick={() => handleSort('day')}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  handleSort('day')
                }
              }}
              data-testid="sort-day"
            >
              Day
              <span className="sort-indicator" aria-hidden="true">
                {sortField === 'day' ? (sortDirection === 'asc' ? '↑' : '↓') : '↕'}
              </span>
            </th>
            
            <th role="columnheader">Actions</th>
          </tr>
        </thead>
        
        <tbody>
          {routes.map((route, index) => (
            <tr 
              key={route.id} 
              role="row"
              aria-rowindex={index + 2}
              data-testid={`route-row-${route.id}`}
            >
              <td role="gridcell">{route.route_name}</td>
              <td role="gridcell">{route.day}</td>
              <td role="gridcell">
                <div role="group" aria-label={`Actions for ${route.route_name}`}>
                  <button
                    aria-label={`Edit ${route.route_name}`}
                    data-testid={`edit-${route.id}`}
                  >
                    Edit
                  </button>
                  <button
                    aria-label={`Delete ${route.route_name}`}
                    data-testid={`delete-${route.id}`}
                  >
                    Delete
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

describe('Accessibility Tests', () => {
  describe('AccessibleRouteCard', () => {
    const mockRoute = createMockRoute({
      id: 1,
      route_name: 'Test Route',
      day: 'Monday',
    })
    
    const mockHandlers = {
      onEdit: jest.fn(),
      onDelete: jest.fn(),
    }
    
    beforeEach(() => {
      jest.clearAllMocks()
    })
    
    it('should have proper ARIA labels and roles', () => {
      render(<AccessibleRouteCard route={mockRoute} {...mockHandlers} />)
      
      expect(screen.getByRole('article')).toBeInTheDocument()
      expect(screen.getByLabelledBy('route-title-1')).toBeInTheDocument()
      expect(screen.getByRole('group', { name: 'Route actions' })).toBeInTheDocument()
    })
    
    it('should have descriptive button labels', () => {
      render(<AccessibleRouteCard route={mockRoute} {...mockHandlers} />)
      
      expect(screen.getByLabelText('Edit route Test Route')).toBeInTheDocument()
      expect(screen.getByLabelText('Delete route Test Route')).toBeInTheDocument()
    })
    
    it('should hide decorative icons from screen readers', () => {
      render(<AccessibleRouteCard route={mockRoute} {...mockHandlers} />)
      
      const icons = document.querySelectorAll('[aria-hidden="true"]')
      expect(icons).toHaveLength(2) // edit and delete icons
    })
    
    it('should provide additional context for destructive actions', () => {
      render(<AccessibleRouteCard route={mockRoute} {...mockHandlers} />)
      
      const deleteButton = screen.getByLabelText('Delete route Test Route')
      expect(deleteButton).toHaveAttribute('aria-describedby', 'delete-warning-1')
      expect(screen.getByText('Warning: This action cannot be undone')).toBeInTheDocument()
    })
  })
  
  describe('AccessibleForm', () => {
    const mockOnSubmit = jest.fn()
    
    beforeEach(() => {
      jest.clearAllMocks()
    })
    
    it('should have proper form structure with fieldset and legend', () => {
      render(<AccessibleForm onSubmit={mockOnSubmit} />)
      
      expect(screen.getByRole('group', { name: 'Route Information' })).toBeInTheDocument()
    })
    
    it('should associate labels with form controls', () => {
      render(<AccessibleForm onSubmit={mockOnSubmit} />)
      
      expect(screen.getByLabelText('Route Name *')).toBeInTheDocument()
      expect(screen.getByLabelText('Day *')).toBeInTheDocument()
    })
    
    it('should indicate required fields', () => {
      render(<AccessibleForm onSubmit={mockOnSubmit} />)
      
      const routeNameInput = screen.getByLabelText('Route Name *')
      const daySelect = screen.getByLabelText('Day *')
      
      expect(routeNameInput).toHaveAttribute('aria-required', 'true')
      expect(daySelect).toHaveAttribute('aria-required', 'true')
    })
    
    it('should show validation errors with proper ARIA attributes', async () => {
      const user = userEvent.setup()
      render(<AccessibleForm onSubmit={mockOnSubmit} />)
      
      // Submit form without filling required fields
      await user.click(screen.getByTestId('submit-button'))
      
      const routeNameInput = screen.getByTestId('route-name-input')
      const routeNameError = screen.getByTestId('route-name-error')
      
      expect(routeNameInput).toHaveAttribute('aria-invalid', 'true')
      expect(routeNameInput).toHaveAttribute('aria-describedby', 'route-name-error')
      expect(routeNameError).toHaveAttribute('role', 'alert')
    })
    
    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()
      render(<AccessibleForm onSubmit={mockOnSubmit} />)
      
      // Tab through form elements
      await user.tab()
      expect(screen.getByTestId('route-name-input')).toHaveFocus()
      
      await user.tab()
      expect(screen.getByTestId('route-day-select')).toHaveFocus()
      
      await user.tab()
      expect(screen.getByTestId('submit-button')).toHaveFocus()
    })
  })
  
  describe('AccessibleDataTable', () => {
    const mockRoutes = [
      createMockRoute({ id: 1, route_name: 'Route A', day: 'Monday' }),
      createMockRoute({ id: 2, route_name: 'Route B', day: 'Tuesday' }),
    ]
    
    const mockOnSort = jest.fn()
    
    beforeEach(() => {
      jest.clearAllMocks()
    })
    
    it('should have proper table structure and ARIA attributes', () => {
      render(<AccessibleDataTable routes={mockRoutes} onSort={mockOnSort} />)
      
      expect(screen.getByRole('region', { name: 'Routes table' })).toBeInTheDocument()
      expect(screen.getByRole('table', { name: 'List of routes' })).toBeInTheDocument()
      expect(screen.getByText(/Routes table with 2 routes/)).toBeInTheDocument()
    })
    
    it('should support keyboard sorting', async () => {
      const user = userEvent.setup()
      render(<AccessibleDataTable routes={mockRoutes} onSort={mockOnSort} />)
      
      const sortButton = screen.getByTestId('sort-route-name')
      
      // Test Enter key
      sortButton.focus()
      await user.keyboard('{Enter}')
      expect(mockOnSort).toHaveBeenCalledWith('route_name', 'asc')
      
      // Test Space key
      await user.keyboard(' ')
      expect(mockOnSort).toHaveBeenCalledWith('route_name', 'desc')
    })
    
    it('should indicate sort state with aria-sort', async () => {
      const user = userEvent.setup()
      render(<AccessibleDataTable routes={mockRoutes} onSort={mockOnSort} />)
      
      const sortButton = screen.getByTestId('sort-route-name')
      
      // Initially no sort
      expect(sortButton).toHaveAttribute('aria-sort', 'none')
      
      // After clicking
      await user.click(sortButton)
      expect(sortButton).toHaveAttribute('aria-sort', 'asc')
    })
    
    it('should provide action context for each row', () => {
      render(<AccessibleDataTable routes={mockRoutes} onSort={mockOnSort} />)
      
      expect(screen.getByRole('group', { name: 'Actions for Route A' })).toBeInTheDocument()
      expect(screen.getByRole('group', { name: 'Actions for Route B' })).toBeInTheDocument()
    })
  })
  
  describe('Keyboard Navigation', () => {
    it('should support tab navigation through interactive elements', async () => {
      const user = userEvent.setup()
      const mockRoute = createMockRoute()
      
      render(
        <div>
          <AccessibleRouteCard route={mockRoute} onEdit={jest.fn()} onDelete={jest.fn()} />
          <AccessibleForm onSubmit={jest.fn()} />
        </div>
      )
      
      // Should be able to tab through all interactive elements
      await user.tab() // Edit button
      await user.tab() // Delete button
      await user.tab() // Route name input
      await user.tab() // Day select
      await user.tab() // Submit button
      await user.tab() // Cancel button
      
      expect(screen.getByTestId('cancel-button')).toHaveFocus()
    })
  })
  
  describe('Screen Reader Support', () => {
    it('should provide meaningful content for screen readers', () => {
      const mockRoute = createMockRoute({
        route_name: 'Morning Route',
        day: 'Monday',
      })
      
      render(<AccessibleRouteCard route={mockRoute} onEdit={jest.fn()} onDelete={jest.fn()} />)
      
      // Check for screen reader only content
      expect(document.querySelector('.sr-only')).toBeInTheDocument()
      
      // Check for proper labeling
      expect(screen.getByLabelText('Edit route Morning Route')).toBeInTheDocument()
      expect(screen.getByLabelText('Delete route Morning Route')).toBeInTheDocument()
    })
  })
})
