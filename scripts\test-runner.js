#!/usr/bin/env node

/**
 * Comprehensive test runner script for EcoGO Choice EMI Dashboard
 * Provides different test execution modes and reporting
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`),
}

// Test execution modes
const modes = {
  unit: {
    name: 'Unit Tests',
    pattern: '__tests__/services/ __tests__/utils/',
    description: 'Run unit tests for services and utilities',
  },
  component: {
    name: 'Component Tests',
    pattern: '__tests__/components/',
    description: 'Run component tests',
  },
  integration: {
    name: 'Integration Tests',
    pattern: '__tests__/integration/',
    description: 'Run integration tests',
  },
  accessibility: {
    name: 'Accessibility Tests',
    pattern: '__tests__/accessibility/',
    description: 'Run accessibility tests',
  },
  performance: {
    name: 'Performance Tests',
    pattern: '__tests__/performance/',
    description: 'Run performance tests',
  },
  all: {
    name: 'All Tests',
    pattern: '__tests__/',
    description: 'Run all tests',
  },
  coverage: {
    name: 'Coverage Report',
    pattern: '__tests__/',
    description: 'Run all tests with coverage report',
    coverage: true,
  },
  watch: {
    name: 'Watch Mode',
    pattern: '__tests__/',
    description: 'Run tests in watch mode',
    watch: true,
  },
}

// Parse command line arguments
const args = process.argv.slice(2)
const verbose = args.includes('--verbose') || args.includes('-v')
const silent = args.includes('--silent') || args.includes('-s')

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
${colors.bright}EcoGO Test Runner${colors.reset}

Usage: node scripts/test-runner.js [mode] [options]

Modes:
${Object.entries(modes).map(([key, config]) =>
  `  ${colors.cyan}${key.padEnd(12)}${colors.reset} ${config.description}`
).join('\n')}

Options:
  --verbose, -v    Show detailed output
  --silent, -s     Suppress output
  --help, -h       Show this help message

Examples:
  node scripts/test-runner.js unit
  node scripts/test-runner.js coverage --verbose
  node scripts/test-runner.js watch
  `)
  process.exit(0)
}

const mode = args.find(arg => !arg.startsWith('-')) || 'all'

// Validate mode
if (!modes[mode]) {
  log.error(`Invalid mode: ${mode}`)
  log.info('Available modes:')
  Object.entries(modes).forEach(([key, config]) => {
    console.log(`  ${colors.cyan}${key}${colors.reset} - ${config.description}`)
  })
  process.exit(1)
}

// Helper functions
const runCommand = (command, options = {}) => {
  try {
    const result = execSync(command, {
      stdio: silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options,
    })
    return { success: true, output: result }
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout }
  }
}

const checkTestFiles = (pattern) => {
  const testFiles = []
  const searchPaths = pattern.split(' ')

  searchPaths.forEach(searchPath => {
    if (fs.existsSync(searchPath)) {
      const files = fs.readdirSync(searchPath, { recursive: true })
      files.forEach(file => {
        if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
          testFiles.push(path.join(searchPath, file))
        }
      })
    }
  })

  return testFiles
}

const generateTestReport = (results) => {
  const reportPath = path.join(process.cwd(), 'test-reports')
  if (!fs.existsSync(reportPath)) {
    fs.mkdirSync(reportPath, { recursive: true })
  }

  const report = {
    timestamp: new Date().toISOString(),
    mode: mode,
    results: results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
    }
  }

  const reportFile = path.join(reportPath, `test-report-${Date.now()}.json`)
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2))

  log.info(`Test report saved to: ${reportFile}`)
  return report
}

const displaySummary = (results) => {
  const passed = results.filter(r => r.success).length
  const failed = results.filter(r => !r.success).length
  const total = results.length

  log.header('Test Summary')
  console.log(`Total: ${total}`)
  console.log(`${colors.green}Passed: ${passed}${colors.reset}`)
  console.log(`${colors.red}Failed: ${failed}${colors.reset}`)

  if (failed > 0) {
    log.header('Failed Tests')
    results.filter(r => !r.success).forEach(result => {
      log.error(result.name)
      if (verbose && result.error) {
        console.log(`  ${result.error}`)
      }
    })
  }
}

// Main execution
const main = async () => {
  const config = modes[mode]

  log.header(`Running ${config.name}`)
  log.info(config.description)

  // Check if test files exist
  const testFiles = checkTestFiles(config.pattern)
  if (testFiles.length === 0) {
    log.warning(`No test files found in pattern: ${config.pattern}`)
    return
  }

  log.info(`Found ${testFiles.length} test files`)

  // Build Jest command
  let jestCommand = 'npx jest'

  if (config.pattern !== '__tests__/') {
    jestCommand += ` ${config.pattern}`
  }

  if (config.coverage) {
    jestCommand += ' --coverage'
  }

  if (config.watch) {
    jestCommand += ' --watch'
  }

  if (verbose) {
    jestCommand += ' --verbose'
  }

  if (silent) {
    jestCommand += ' --silent'
  }

  // Add additional Jest options
  jestCommand += ' --passWithNoTests'

  log.info(`Executing: ${jestCommand}`)

  // Run tests
  const startTime = Date.now()
  const result = runCommand(jestCommand)
  const endTime = Date.now()
  const duration = ((endTime - startTime) / 1000).toFixed(2)

  // Process results
  if (result.success) {
    log.success(`Tests completed successfully in ${duration}s`)
  } else {
    log.error(`Tests failed after ${duration}s`)
    if (verbose && result.error) {
      console.log(result.error)
    }
  }

  // Generate report for non-watch modes
  if (!config.watch) {
    const testResults = [{
      name: config.name,
      success: result.success,
      duration: duration,
      error: result.error,
    }]

    generateTestReport(testResults)
    displaySummary(testResults)
  }

  // Exit with appropriate code
  process.exit(result.success ? 0 : 1)
}

// Handle process signals
process.on('SIGINT', () => {
  log.info('\nTest execution interrupted')
  process.exit(1)
})

process.on('SIGTERM', () => {
  log.info('\nTest execution terminated')
  process.exit(1)
})



// Run the main function
main().catch(error => {
  log.error(`Unexpected error: ${error.message}`)
  process.exit(1)
})
