# EcoGO Testing Strategy

## Overview
Comprehensive Jest-based testing setup for the Choice EMI Dashboard with React Testing Library, focusing on robust, maintainable test coverage across components, services, and user workflows.

## Testing Framework Stack
- **Jest**: Primary testing framework with Next.js integration
- **React Testing Library**: Component testing with user-centric approach
- **Jest DOM**: Extended matchers for DOM testing
- **User Event**: Realistic user interaction simulation
- **MSW (Future)**: API mocking for integration tests

## Test Structure

### 1. Unit Tests
**Location**: `__tests__/services/`
**Purpose**: Test individual functions and service methods in isolation
**Coverage**: API services, utility functions, data transformations

**Example**:
```typescript
// __tests__/services/route-service.test.ts
describe('getRouteDetails', () => {
  it('should fetch route details successfully', async () => {
    mockAxiosInstance.get.mockResolvedValue(mockResponse)
    const result = await getRouteDetails(1)
    expect(result).toEqual(expectedData)
  })
})
```

### 2. Component Tests
**Location**: `__tests__/components/`
**Purpose**: Test React components with user interactions
**Coverage**: Props handling, state changes, event handlers, conditional rendering

**Example**:
```typescript
// __tests__/components/route/RouteCreate.test.tsx
it('handles form submission successfully', async () => {
  render(<RouteCreate {...props} />)
  await user.click(screen.getByText('Create'))
  expect(mockCreateRoute).toHaveBeenCalled()
})
```

### 3. Page Tests
**Location**: `__tests__/pages/`
**Purpose**: Test full page components with complex interactions
**Coverage**: Data loading, form handling, navigation, error states

### 4. Integration Tests
**Location**: `__tests__/integration/`
**Purpose**: Test complete user workflows across multiple components
**Coverage**: End-to-end scenarios, cross-component communication

## Test Configuration

### Jest Configuration (`jest.config.js`)
- **Environment**: jsdom for browser simulation
- **Setup**: Automated mocking of Next.js components
- **Coverage**: 70% threshold for branches, functions, lines, statements
- **Transforms**: TypeScript and JSX support

### Setup File (`jest.setup.js`)
- **Global Mocks**: Next.js router, Image component, environment variables
- **DOM APIs**: IntersectionObserver, ResizeObserver, matchMedia
- **Library Mocks**: react-beautiful-dnd, Material-UI components

## Mock Strategy

### 1. Service Mocks
**File**: `__tests__/mocks/route-service.mock.ts`
**Purpose**: Consistent API response mocking
**Features**:
- Predefined mock data
- Configurable responses
- Error simulation
- Reset utilities

### 2. Context Mocks
**Implementation**: Jest mocks for React contexts
**Coverage**: CommonContext, Alert context, authentication

### 3. Component Mocks
**Strategy**: Mock complex third-party components
**Examples**: Material-UI Autocomplete, react-beautiful-dnd

## Test Utilities

### Custom Render (`__tests__/utils/test-utils.tsx`)
**Features**:
- Provider wrapping (Theme, Context)
- Custom matchers
- Test data factories
- Form interaction helpers

**Usage**:
```typescript
import { render, screen } from '../utils/test-utils'
const mockRoute = createMockRoute({ route_name: 'Test Route' })
```

## Testing Patterns

### 1. Arrange-Act-Assert Pattern
```typescript
it('should handle customer deletion', async () => {
  // Arrange
  render(<RouteDetails />)
  await waitFor(() => screen.getByText('John Doe'))
  
  // Act
  await user.click(screen.getByText('delete'))
  
  // Assert
  expect(mockFire).toHaveBeenCalledWith(
    expect.objectContaining({ title: 'Are You Sure' })
  )
})
```

### 2. Error Boundary Testing
```typescript
it('handles API errors gracefully', async () => {
  mockService.getRouteDetails.mockRejectedValue(new Error('API Error'))
  render(<RouteDetails />)
  
  await waitFor(() => {
    expect(mockFire).toHaveBeenCalledWith(
      expect.objectContaining({ icon: 'error' })
    )
  })
})
```

### 3. Form Validation Testing
```typescript
it('validates required fields', async () => {
  render(<RouteCreate />)
  await user.click(screen.getByText('Create'))
  
  expect(screen.getByText('Route title is required')).toBeInTheDocument()
})
```

## Coverage Requirements

### Minimum Coverage Thresholds
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Priority Areas (90%+ Coverage)
- Service layer functions
- Form validation logic
- Error handling
- Critical user workflows

### Exclusions
- Configuration files
- Type definitions
- Layout components
- Third-party integrations

## Running Tests

### Commands
```bash
# Run all tests
npm test

# Watch mode for development
npm run test:watch

# Generate coverage report
npm run test:coverage

# CI/CD pipeline
npm run test:ci
```

### Test Organization
- **Unit tests**: Fast, isolated, no external dependencies
- **Integration tests**: Slower, test component interactions
- **E2E tests**: (Future) Full application workflows

## Best Practices

### 1. Test Naming
- Descriptive test names explaining the scenario
- Use "should" statements for expected behavior
- Group related tests with `describe` blocks

### 2. Mock Management
- Reset mocks between tests
- Use specific mock implementations per test
- Avoid over-mocking (test real behavior when possible)

### 3. Async Testing
- Always use `waitFor` for async operations
- Test loading states and error conditions
- Handle race conditions properly

### 4. User-Centric Testing
- Test from user perspective, not implementation details
- Use accessible queries (getByRole, getByLabelText)
- Simulate real user interactions

## Continuous Integration

### Pre-commit Hooks
- Run tests on changed files
- Enforce coverage thresholds
- Lint test files

### CI Pipeline
- Run full test suite on pull requests
- Generate and publish coverage reports
- Fail builds on test failures or coverage drops

## Future Enhancements

### 1. Visual Regression Testing
- Screenshot comparison for UI components
- Automated visual diff detection

### 2. Performance Testing
- Component render performance
- Memory leak detection
- Bundle size impact

### 3. Accessibility Testing
- Automated a11y testing with jest-axe
- Screen reader compatibility
- Keyboard navigation testing

### 4. API Integration Testing
- Mock Service Worker (MSW) implementation
- Real API endpoint testing in staging
- Contract testing with backend

## Debugging Tests

### Common Issues
1. **Async timing**: Use `waitFor` instead of `setTimeout`
2. **Mock persistence**: Reset mocks in `beforeEach`
3. **DOM cleanup**: React Testing Library handles automatically
4. **Context providers**: Use custom render with providers

### Debug Tools
- `screen.debug()`: Print current DOM state
- `logRoles()`: Show available accessibility roles
- Jest `--verbose` flag for detailed output
- VS Code Jest extension for inline debugging
