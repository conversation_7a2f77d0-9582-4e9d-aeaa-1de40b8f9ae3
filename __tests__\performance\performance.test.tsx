import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { createMockCustomerList, createMockRoute } from '../mocks/route-service.mock'

// Mock performance monitoring utilities
const measureRenderTime = (componentName: string, renderFn: () => void) => {
  const startTime = performance.now()
  renderFn()
  const endTime = performance.now()
  const renderTime = endTime - startTime
  
  console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`)
  return renderTime
}

const measureMemoryUsage = () => {
  if ('memory' in performance) {
    return {
      usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
      totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
      jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
    }
  }
  return null
}

// Mock components for performance testing
const LargeDataList = ({ items, renderItem }) => {
  return (
    <div data-testid="large-data-list">
      {items.map((item, index) => (
        <div key={item.id || index} data-testid={`list-item-${index}`}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  )
}

const VirtualizedList = ({ items, renderItem, itemHeight = 50, containerHeight = 400 }) => {
  const [scrollTop, setScrollTop] = React.useState(0)
  
  const startIndex = Math.floor(scrollTop / itemHeight)
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  )
  
  const visibleItems = items.slice(startIndex, endIndex)
  
  return (
    <div 
      data-testid="virtualized-list"
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={(e) => setScrollTop(e.target.scrollTop)}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => (
          <div
            key={item.id || (startIndex + index)}
            style={{
              position: 'absolute',
              top: (startIndex + index) * itemHeight,
              height: itemHeight,
              width: '100%',
            }}
            data-testid={`virtual-item-${startIndex + index}`}
          >
            {renderItem(item, startIndex + index)}
          </div>
        ))}
      </div>
    </div>
  )
}

const MemoizedComponent = React.memo(({ data, onAction }) => {
  return (
    <div data-testid="memoized-component">
      <h3>{data.title}</h3>
      <p>{data.description}</p>
      <button onClick={() => onAction(data.id)}>Action</button>
    </div>
  )
})

const ExpensiveComponent = ({ items, shouldRecalculate }) => {
  const expensiveCalculation = React.useMemo(() => {
    // Simulate expensive calculation
    let result = 0
    for (let i = 0; i < items.length * 1000; i++) {
      result += Math.random()
    }
    return result
  }, [items, shouldRecalculate])
  
  return (
    <div data-testid="expensive-component">
      <p>Calculated value: {expensiveCalculation.toFixed(2)}</p>
      <p>Items count: {items.length}</p>
    </div>
  )
}

const LazyLoadedComponent = React.lazy(() => 
  Promise.resolve({
    default: ({ message }) => (
      <div data-testid="lazy-component">{message}</div>
    )
  })
)

describe('Performance Tests', () => {
  describe('Large Data Rendering', () => {
    it('should render large lists efficiently', () => {
      const largeDataSet = createMockCustomerList(1000)
      
      const renderTime = measureRenderTime('LargeDataList', () => {
        render(
          <LargeDataList 
            items={largeDataSet}
            renderItem={(item) => <span>{item.name}</span>}
          />
        )
      })
      
      // Expect render time to be reasonable (adjust threshold as needed)
      expect(renderTime).toBeLessThan(1000) // 1 second
      expect(screen.getByTestId('large-data-list')).toBeInTheDocument()
    })
    
    it('should handle virtualized lists for better performance', () => {
      const largeDataSet = createMockCustomerList(10000)
      
      const renderTime = measureRenderTime('VirtualizedList', () => {
        render(
          <VirtualizedList 
            items={largeDataSet}
            renderItem={(item) => <span>{item.name}</span>}
            itemHeight={50}
            containerHeight={400}
          />
        )
      })
      
      // Virtualized list should render faster than regular list
      expect(renderTime).toBeLessThan(100) // 100ms
      
      // Should only render visible items
      const renderedItems = screen.getAllByTestId(/virtual-item-/)
      expect(renderedItems.length).toBeLessThan(20) // Only visible items
    })
  })
  
  describe('Component Memoization', () => {
    it('should prevent unnecessary re-renders with React.memo', () => {
      const mockData = { id: 1, title: 'Test', description: 'Description' }
      const mockAction = jest.fn()
      
      const { rerender } = render(
        <MemoizedComponent data={mockData} onAction={mockAction} />
      )
      
      // Re-render with same props
      rerender(<MemoizedComponent data={mockData} onAction={mockAction} />)
      
      // Component should not re-render unnecessarily
      expect(screen.getByTestId('memoized-component')).toBeInTheDocument()
    })
    
    it('should re-render when props change', () => {
      const mockData1 = { id: 1, title: 'Test 1', description: 'Description 1' }
      const mockData2 = { id: 2, title: 'Test 2', description: 'Description 2' }
      const mockAction = jest.fn()
      
      const { rerender } = render(
        <MemoizedComponent data={mockData1} onAction={mockAction} />
      )
      
      expect(screen.getByText('Test 1')).toBeInTheDocument()
      
      // Re-render with different props
      rerender(<MemoizedComponent data={mockData2} onAction={mockAction} />)
      
      expect(screen.getByText('Test 2')).toBeInTheDocument()
    })
  })
  
  describe('Expensive Calculations', () => {
    it('should memoize expensive calculations', () => {
      const items = createMockCustomerList(100)
      
      const startTime = performance.now()
      
      const { rerender } = render(
        <ExpensiveComponent items={items} shouldRecalculate={false} />
      )
      
      const firstRenderTime = performance.now() - startTime
      
      // Re-render with same dependencies
      const rerenderStartTime = performance.now()
      rerender(<ExpensiveComponent items={items} shouldRecalculate={false} />)
      const rerenderTime = performance.now() - rerenderStartTime
      
      // Second render should be much faster due to memoization
      expect(rerenderTime).toBeLessThan(firstRenderTime / 2)
    })
    
    it('should recalculate when dependencies change', () => {
      const items1 = createMockCustomerList(50)
      const items2 = createMockCustomerList(100)
      
      const { rerender } = render(
        <ExpensiveComponent items={items1} shouldRecalculate={false} />
      )
      
      expect(screen.getByText('Items count: 50')).toBeInTheDocument()
      
      // Change dependencies
      rerender(<ExpensiveComponent items={items2} shouldRecalculate={true} />)
      
      expect(screen.getByText('Items count: 100')).toBeInTheDocument()
    })
  })
  
  describe('Lazy Loading', () => {
    it('should load components lazily', async () => {
      render(
        <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
          <LazyLoadedComponent message="Lazy loaded content" />
        </React.Suspense>
      )
      
      // Should show loading state first
      expect(screen.getByTestId('loading')).toBeInTheDocument()
      
      // Then show the lazy component
      await waitFor(() => {
        expect(screen.getByTestId('lazy-component')).toBeInTheDocument()
        expect(screen.getByText('Lazy loaded content')).toBeInTheDocument()
      })
    })
  })
  
  describe('Memory Usage', () => {
    it('should not cause memory leaks with large datasets', () => {
      const initialMemory = measureMemoryUsage()
      
      // Render and unmount large components multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(
          <LargeDataList 
            items={createMockCustomerList(500)}
            renderItem={(item) => <div>{item.name}</div>}
          />
        )
        unmount()
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = measureMemoryUsage()
      
      if (initialMemory && finalMemory) {
        const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize
        const memoryIncreasePercent = (memoryIncrease / initialMemory.usedJSHeapSize) * 100
        
        // Memory increase should be reasonable (less than 50%)
        expect(memoryIncreasePercent).toBeLessThan(50)
      }
    })
  })
  
  describe('Render Performance Benchmarks', () => {
    it('should render customer cards within performance budget', () => {
      const customers = createMockCustomerList(50)
      
      const renderTime = measureRenderTime('CustomerCards', () => {
        render(
          <div>
            {customers.map(customer => (
              <div key={customer.id} data-testid={`customer-${customer.id}`}>
                <h3>{customer.name}</h3>
                <p>{customer.location}</p>
                <p>{customer.address}</p>
              </div>
            ))}
          </div>
        )
      })
      
      // Should render 50 customer cards in under 200ms
      expect(renderTime).toBeLessThan(200)
    })
    
    it('should handle rapid state updates efficiently', async () => {
      const TestComponent = () => {
        const [count, setCount] = React.useState(0)
        
        React.useEffect(() => {
          const interval = setInterval(() => {
            setCount(c => c + 1)
          }, 10)
          
          setTimeout(() => clearInterval(interval), 100)
          
          return () => clearInterval(interval)
        }, [])
        
        return <div data-testid="counter">{count}</div>
      }
      
      const startTime = performance.now()
      render(<TestComponent />)
      
      await waitFor(() => {
        expect(parseInt(screen.getByTestId('counter').textContent)).toBeGreaterThan(5)
      })
      
      const endTime = performance.now()
      const totalTime = endTime - startTime
      
      // Should handle rapid updates efficiently
      expect(totalTime).toBeLessThan(500)
    })
  })
  
  describe('Bundle Size Impact', () => {
    it('should not import unnecessary dependencies', () => {
      // This test would typically be run with bundle analysis tools
      // Here we simulate checking for common performance anti-patterns
      
      const componentCode = `
        import React from 'react'
        // Good: specific imports
        import { Button } from '@mui/material'
        // Bad: importing entire library
        // import * as MUI from '@mui/material'
        
        const TestComponent = () => <Button>Test</Button>
      `
      
      // Check for anti-patterns
      expect(componentCode).not.toContain('import *')
      expect(componentCode).toContain('import { Button }')
    })
  })
  
  describe('Network Performance', () => {
    it('should handle API response delays gracefully', async () => {
      const slowApiCall = () => new Promise(resolve => 
        setTimeout(() => resolve({ data: 'response' }), 2000)
      )
      
      const TestComponent = () => {
        const [data, setData] = React.useState(null)
        const [loading, setLoading] = React.useState(true)
        
        React.useEffect(() => {
          slowApiCall().then(response => {
            setData(response.data)
            setLoading(false)
          })
        }, [])
        
        if (loading) return <div data-testid="loading">Loading...</div>
        return <div data-testid="data">{data}</div>
      }
      
      render(<TestComponent />)
      
      // Should show loading state
      expect(screen.getByTestId('loading')).toBeInTheDocument()
      
      // Should handle the delay and show data
      await waitFor(() => {
        expect(screen.getByTestId('data')).toBeInTheDocument()
      }, { timeout: 3000 })
    })
  })
})
