import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import RouteDetailsPage from '@/app/pages/route/routeDetails/page'
import { 
  mockRouteService, 
  mockRouteDetails, 
  mockCustomerList,
  mockBranchList,
  mockAgentList,
  resetRouteServiceMocks 
} from '../../mocks/route-service.mock'

// Mock the route service
jest.mock('@/app/pages/route/route-service', () => mockRouteService)
jest.mock('@/app/pages/agent/agent-service', () => ({
  getBrachList: mockRouteService.getBrachList,
}))

// Mock contexts
const mockSetIsLoading = jest.fn()
const mockFire = jest.fn()

jest.mock('@/app/contexts/commonContext', () => ({
  useCommonContext: () => ({
    setIsLoading: mockSetIsLoading,
    screenSize: 1920,
  }),
}))

jest.mock('@/app/components/utilities/Alert/Alert', () => ({
  useAlert: () => ({
    fire: mockFire,
  }),
}))

// Mock react-hook-form
const mockSetValue = jest.fn()
const mockGetValues = jest.fn()
const mockHandleSubmit = jest.fn()
const mockRegister = jest.fn()

jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: mockHandleSubmit,
    formState: { errors: {} },
    setValue: mockSetValue,
    getValues: mockGetValues,
    register: mockRegister,
  }),
  Controller: ({ render }: any) => render({ field: {} }),
  SubmitHandler: jest.fn(),
}))

describe('RouteDetailsPage', () => {
  beforeEach(() => {
    resetRouteServiceMocks()
    jest.clearAllMocks()
    
    // Setup default mock returns
    mockRouteService.getRouteDetails.mockResolvedValue(mockRouteDetails)
    mockRouteService.fetchCustomerList.mockResolvedValue(mockCustomerList)
    mockRouteService.getBrachList.mockResolvedValue({ results: mockBranchList })
    mockRouteService.getAgentsList.mockResolvedValue({ results: mockAgentList })
  })

  it('renders route details page correctly', async () => {
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Route')).toBeInTheDocument()
      expect(screen.getByText('Monday')).toBeInTheDocument()
    })
  })

  it('loads and displays route customers', async () => {
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    })
  })

  it('displays customer priority numbers correctly', async () => {
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument() // Priority for John Doe
      expect(screen.getByText('2')).toBeInTheDocument() // Priority for Jane Smith
    })
  })

  it('shows new customer indicator', async () => {
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('New')).toBeInTheDocument() // For Jane Smith
    })
  })

  it('handles customer deletion', async () => {
    const user = userEvent.setup()
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })

    // Find and click delete button for first customer
    const deleteButtons = screen.getAllByText('delete')
    await user.click(deleteButtons[0])

    // Verify confirmation dialog is triggered
    expect(mockFire).toHaveBeenCalledWith(
      expect.objectContaining({
        position: 'center',
        icon: 'info',
        title: 'Are You Sure',
        text: 'Remove the customer from the route?',
      })
    )
  })

  it('handles adding new customer', async () => {
    const user = userEvent.setup()
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('+ Add Customer')).toBeInTheDocument()
    })

    const addButton = screen.getByText('+ Add Customer')
    await user.click(addButton)

    // Should show error if no customer selected
    await waitFor(() => {
      expect(screen.getByText('Please select a customer before adding.')).toBeInTheDocument()
    })
  })

  it('handles form submission', async () => {
    const user = userEvent.setup()
    mockHandleSubmit.mockImplementation((callback) => (e) => {
      e.preventDefault()
      callback({
        route_name: 'Updated Route',
        day: 'Tuesday',
        agent_id: 2,
        branch_id: 2,
      })
    })

    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Save')).toBeInTheDocument()
    })

    const saveButton = screen.getByText('Save')
    await user.click(saveButton)

    await waitFor(() => {
      expect(mockRouteService.editRoute).toHaveBeenCalled()
    })
  })

  it('handles cancel action', async () => {
    const user = userEvent.setup()
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Cancel')).toBeInTheDocument()
    })

    const cancelButton = screen.getByText('Cancel')
    await user.click(cancelButton)

    expect(mockFire).toHaveBeenCalledWith(
      expect.objectContaining({
        position: 'center',
        icon: 'info',
        title: 'Are You Sure',
        text: 'The current changes will be lost.',
      })
    )
  })

  it('filters customers by branch', async () => {
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(mockRouteService.fetchCustomerList).toHaveBeenCalled()
    })

    // Verify that customer filtering logic is applied
    expect(mockRouteService.getRouteDetails).toHaveBeenCalled()
  })

  it('handles branch change with customer validation', async () => {
    // Test the branch change validation logic
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Route')).toBeInTheDocument()
    })

    // This would test the handleBranchChange function
    // The actual implementation would depend on how the Autocomplete is set up
  })

  it('handles API errors gracefully', async () => {
    mockRouteService.getRouteDetails.mockRejectedValue(new Error('API Error'))
    
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(mockFire).toHaveBeenCalledWith(
        expect.objectContaining({
          icon: 'error',
          title: 'Operation Failed',
          text: 'Something went wrong. Please try again.',
        })
      )
    })
  })

  it('handles drag and drop reordering', async () => {
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })

    // Test drag and drop functionality
    // Note: react-beautiful-dnd is mocked, so this tests the component structure
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
  })

  it('displays no customers message when route is empty', async () => {
    mockRouteService.getRouteDetails.mockResolvedValue({
      ...mockRouteDetails,
      route_customers: [],
    })
    
    render(<RouteDetailsPage />)
    
    await waitFor(() => {
      expect(screen.getByText('No customers have been assigned to this route.')).toBeInTheDocument()
    })
  })
})
