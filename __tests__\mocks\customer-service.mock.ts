import { Customer_List_Item } from '@/app/pages/customer/customer.model'

// Mock customer data
export const mockCustomers: Customer_List_Item[] = [
  {
    id: 1,
    name: '<PERSON>',
    short_title: 'Mr. <PERSON>',
    priority: 1,
    profile_photo: 'profile1.jpg',
    location: 'Downtown',
    address: '123 Main St, City',
    is_new_customer: false,
    emi_type: 'monthly',
    emi_period_type: 'monthly',
    next_emi_due_date: '2024-01-15',
    star_rating: 4,
    branch_id: 1,
  },
  {
    id: 2,
    name: '<PERSON>',
    short_title: '<PERSON><PERSON> <PERSON>',
    priority: 2,
    profile_photo: 'profile2.jpg',
    location: 'Uptown',
    address: '456 Oak Ave, City',
    is_new_customer: true,
    emi_type: 'weekly',
    emi_period_type: 'weekly',
    next_emi_due_date: '2024-01-20',
    star_rating: 5,
    branch_id: 1,
  },
  {
    id: 3,
    name: '<PERSON>',
    short_title: 'Mr. <PERSON>',
    priority: 0,
    profile_photo: 'profile3.jpg',
    location: 'Midtown',
    address: '789 Pine Rd, City',
    is_new_customer: false,
    emi_type: 'monthly',
    emi_period_type: 'monthly',
    next_emi_due_date: '2024-01-25',
    star_rating: 3,
    branch_id: 2,
  },
]

export const mockCustomerDetails = {
  id: 1,
  name: 'John Doe',
  short_title: 'Mr. John',
  profile_photo: 'profile1.jpg',
  location: 'Downtown',
  address: '123 Main St, City',
  phone: '+1234567890',
  email: '<EMAIL>',
  is_new_customer: false,
  emi_type: 'monthly',
  emi_period_type: 'monthly',
  next_emi_due_date: '2024-01-15',
  star_rating: 4,
  branch_id: 1,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

// Mock service functions
export const mockCustomerService = {
  getCustomerList: jest.fn().mockResolvedValue({
    results: mockCustomers,
    total_pages: 1,
    total: mockCustomers.length,
    page: 1,
    page_size: 10,
    links: { next: '', previous: '' },
  }),
  getCustomerDetails: jest.fn().mockResolvedValue(mockCustomerDetails),
  createCustomer: jest.fn().mockResolvedValue({ data: { success: true } }),
  updateCustomer: jest.fn().mockResolvedValue({ data: { success: true } }),
  deleteCustomer: jest.fn().mockResolvedValue({ data: { success: true } }),
  searchCustomers: jest.fn().mockResolvedValue({
    results: mockCustomers.filter(c => c.name.toLowerCase().includes('john')),
    total_pages: 1,
    total: 1,
    page: 1,
    page_size: 10,
    links: { next: '', previous: '' },
  }),
}

// Mock axios responses
export const mockCustomerAxiosResponses = {
  getCustomerList: {
    data: {
      results: mockCustomers,
      total_pages: 1,
      total: mockCustomers.length,
      page: 1,
      page_size: 10,
      links: { next: '', previous: '' },
    },
    status: 200,
    statusText: 'OK',
  },
  getCustomerDetails: {
    data: mockCustomerDetails,
    status: 200,
    statusText: 'OK',
  },
  createCustomer: {
    data: { success: true, id: 4 },
    status: 201,
    statusText: 'Created',
  },
  updateCustomer: {
    data: { success: true },
    status: 200,
    statusText: 'OK',
  },
  deleteCustomer: {
    data: { success: true },
    status: 200,
    statusText: 'OK',
  },
  error: {
    response: {
      data: { detail: 'Customer not found' },
      status: 404,
      statusText: 'Not Found',
    },
    message: 'Request failed with status code 404',
  },
}

// Jest mock setup for customer service
export const setupCustomerServiceMocks = () => {
  jest.mock('@/app/pages/customer/customer-service', () => mockCustomerService)
}

// Reset all mocks
export const resetCustomerServiceMocks = () => {
  Object.values(mockCustomerService).forEach(mock => {
    if (jest.isMockFunction(mock)) {
      mock.mockClear()
    }
  })
}

// Test data factories
export const createMockCustomer = (overrides = {}) => ({
  id: Math.floor(Math.random() * 1000),
  name: 'Test Customer',
  short_title: 'Mr. Test',
  priority: 0,
  profile_photo: 'test-profile.jpg',
  location: 'Test Location',
  address: 'Test Address',
  is_new_customer: false,
  emi_type: 'monthly',
  emi_period_type: 'monthly',
  next_emi_due_date: '2024-01-30',
  star_rating: 3,
  branch_id: 1,
  ...overrides,
})

export const createMockCustomerList = (count = 3) => {
  return Array.from({ length: count }, (_, index) => 
    createMockCustomer({ 
      id: index + 1, 
      name: `Customer ${index + 1}`,
      priority: index + 1,
    })
  )
}

// Branch filtering utilities for testing
export const filterCustomersByBranch = (customers: Customer_List_Item[], branchId: number) => {
  return customers.filter(customer => customer.branch_id === branchId)
}

export const sortCustomersByPriority = (customers: Customer_List_Item[]) => {
  return [...customers].sort((a, b) => a.priority - b.priority)
}

// Validation test helpers
export const validateCustomerData = (customer: Partial<Customer_List_Item>) => {
  const errors: Record<string, string> = {}
  
  if (!customer.name?.trim()) {
    errors.name = 'Customer name is required'
  }
  
  if (!customer.location?.trim()) {
    errors.location = 'Location is required'
  }
  
  if (!customer.address?.trim()) {
    errors.address = 'Address is required'
  }
  
  if (!customer.branch_id) {
    errors.branch_id = 'Branch is required'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}
