import axios from 'axios'
import {
  getRouteList,
  getRouteDetails,
  createRoute,
  editRoute,
  deleteRoute,
  fetchCustomerList,
  fetchBranchList,
  fetchAgentList,
  getAgentsList,
} from '@/app/pages/route/route-service'
import { mockAxiosResponses } from '../mocks/route-service.mock'

// Mock axios
jest.mock('@/app/api/axiosInstance', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}))

const mockedAxios = axios as jest.Mocked<typeof axios>
const mockAxiosInstance = require('@/app/api/axiosInstance')

describe('Route Service', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getRouteList', () => {
    it('should fetch route list successfully', async () => {
      const mockResponse = {
        data: {
          results: [],
          total_pages: 1,
          total: 0,
          page: 1,
          page_size: 10,
          links: { next: '', previous: '' },
        },
      }
      mockAxiosInstance.get.mockResolvedValue(mockResponse)

      const result = await getRouteList(0, 10, 'test', 'Monday')

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        'dashboard/route_list/?skip=0&limit=10&search=test&day=Monday'
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle API errors', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Network Error'))

      await expect(getRouteList(0, 10, '', '')).rejects.toThrow('Failed to fetch route list')
    })

    it('should build query params correctly without day filter', async () => {
      const mockResponse = { data: { results: [] } }
      mockAxiosInstance.get.mockResolvedValue(mockResponse)

      await getRouteList(10, 20, 'search term')

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        'dashboard/route_list/?skip=10&limit=20&search=search+term'
      )
    })
  })

  describe('getRouteDetails', () => {
    it('should fetch route details successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue(mockAxiosResponses.getRouteDetails)

      const result = await getRouteDetails(1)

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('dashboard/route_detail/1')
      expect(result).toEqual(mockAxiosResponses.getRouteDetails.data)
    })

    it('should handle null id', async () => {
      mockAxiosInstance.get.mockResolvedValue(mockAxiosResponses.getRouteDetails)

      await getRouteDetails(null)

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('dashboard/route_detail/null')
    })

    it('should propagate API errors', async () => {
      const error = new Error('Route not found')
      mockAxiosInstance.get.mockRejectedValue(error)

      await expect(getRouteDetails(999)).rejects.toThrow('Route not found')
    })
  })

  describe('createRoute', () => {
    it('should create route successfully', async () => {
      const routeData = {
        route_name: 'New Route',
        day: 'Monday',
        agent_id: 1,
        branch_id: 1,
      }
      mockAxiosInstance.post.mockResolvedValue({ data: { success: true } })

      const result = await createRoute(routeData)

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('create_route', routeData)
      expect(result).toEqual({ success: true })
    })

    it('should handle creation errors', async () => {
      const routeData = {
        route_name: 'New Route',
        day: 'Monday',
      }
      mockAxiosInstance.post.mockRejectedValue(new Error('Validation Error'))

      await expect(createRoute(routeData)).rejects.toThrow('Failed to create new route')
    })
  })

  describe('editRoute', () => {
    it('should edit route successfully', async () => {
      const routeData = {
        route_name: 'Updated Route',
        day: 'Tuesday',
        agent_id: 2,
        branch_id: 2,
        customers: [
          { customer_id: 1, priority: 1 },
          { customer_id: 2, priority: 2 },
        ],
      }
      mockAxiosInstance.put.mockResolvedValue(mockAxiosResponses.editRoute)

      const result = await editRoute(1, routeData)

      expect(mockAxiosInstance.put).toHaveBeenCalledWith('update_route/1', routeData)
      expect(result).toEqual(mockAxiosResponses.editRoute)
    })

    it('should handle null route id', async () => {
      const routeData = {
        route_name: 'Updated Route',
        day: 'Tuesday',
        agent_id: 2,
        branch_id: 2,
        customers: [],
      }
      mockAxiosInstance.put.mockResolvedValue(mockAxiosResponses.editRoute)

      await editRoute(null, routeData)

      expect(mockAxiosInstance.put).toHaveBeenCalledWith('update_route/null', routeData)
    })

    it('should propagate edit errors', async () => {
      const error = new Error('Update failed')
      mockAxiosInstance.put.mockRejectedValue(error)

      await expect(editRoute(1, {} as any)).rejects.toThrow('Update failed')
    })
  })

  describe('deleteRoute', () => {
    it('should delete route successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValue({ data: { success: true } })

      const result = await deleteRoute(1)

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/delete_route/1')
      expect(result).toEqual({ data: { success: true } })
    })

    it('should handle deletion errors', async () => {
      mockAxiosInstance.delete.mockRejectedValue(new Error('Delete failed'))

      await expect(deleteRoute(1)).rejects.toThrow('Failed to delete the route')
    })
  })

  describe('fetchCustomerList', () => {
    it('should fetch customer list successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue(mockAxiosResponses.fetchCustomerList)

      const result = await fetchCustomerList()

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('dashboard/customers/?pagination=false')
      expect(result).toEqual(mockAxiosResponses.fetchCustomerList.data)
    })

    it('should handle customer list errors', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Network Error'))

      await expect(fetchCustomerList()).rejects.toThrow('Failed to fetch customer list')
    })
  })

  describe('fetchBranchList', () => {
    it('should fetch branch list successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue(mockAxiosResponses.getBranchList)

      const result = await fetchBranchList()

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/branches/?pagination=false')
      expect(result).toEqual(mockAxiosResponses.getBranchList.data)
    })

    it('should handle branch list errors', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Network Error'))

      await expect(fetchBranchList()).rejects.toThrow('Failed to fetch branch list')
    })
  })

  describe('fetchAgentList', () => {
    it('should fetch agent list successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue(mockAxiosResponses.getAgentsList)

      const result = await fetchAgentList()

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('agents/?pagination=false')
      expect(result).toEqual(mockAxiosResponses.getAgentsList.data)
    })

    it('should handle agent list errors', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Network Error'))

      await expect(fetchAgentList()).rejects.toThrow('Failed to fetch agent list')
    })
  })

  describe('getAgentsList', () => {
    it('should fetch agents list successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue(mockAxiosResponses.getAgentsList)

      const result = await getAgentsList()

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('agents/?pagination=false')
      expect(result).toEqual(mockAxiosResponses.getAgentsList.data)
    })

    it('should propagate agents list errors', async () => {
      const error = new Error('Network Error')
      mockAxiosInstance.get.mockRejectedValue(error)

      await expect(getAgentsList()).rejects.toThrow('Network Error')
    })
  })
})
