import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { createMockCustomer } from '../../mocks/customer-service.mock'

// Mock CustomerCard component (since we don't have the actual component)
const MockCustomerCard = ({ customer, onEdit, onDelete, onView }) => {
  return (
    <div data-testid={`customer-card-${customer.id}`} className="customer-card">
      <div className="customer-header">
        <img 
          src={customer.profile_photo} 
          alt={`${customer.name} profile`}
          data-testid="customer-avatar"
        />
        <div className="customer-info">
          <h3 data-testid="customer-name">{customer.name}</h3>
          <p data-testid="customer-title">{customer.short_title}</p>
          {customer.is_new_customer && (
            <span data-testid="new-customer-badge" className="new-badge">
              New
            </span>
          )}
        </div>
        <div className="customer-rating">
          {Array.from({ length: 5 }, (_, index) => (
            <span 
              key={index}
              data-testid={`star-${index + 1}`}
              className={index < customer.star_rating ? 'star-filled' : 'star-empty'}
            >
              ★
            </span>
          ))}
        </div>
      </div>
      
      <div className="customer-details">
        <p data-testid="customer-location">📍 {customer.location}</p>
        <p data-testid="customer-address">{customer.address}</p>
        <p data-testid="customer-emi">
          EMI: {customer.emi_type} - Due: {customer.next_emi_due_date}
        </p>
        {customer.priority > 0 && (
          <div data-testid="priority-badge" className="priority-badge">
            Priority: {customer.priority}
          </div>
        )}
      </div>
      
      <div className="customer-actions">
        <button 
          onClick={() => onView?.(customer.id)}
          data-testid="view-customer-button"
          className="action-button view"
        >
          View
        </button>
        <button 
          onClick={() => onEdit?.(customer.id)}
          data-testid="edit-customer-button"
          className="action-button edit"
        >
          Edit
        </button>
        <button 
          onClick={() => onDelete?.(customer.id)}
          data-testid="delete-customer-button"
          className="action-button delete"
        >
          Delete
        </button>
      </div>
    </div>
  )
}

describe('CustomerCard Component', () => {
  const mockOnEdit = jest.fn()
  const mockOnDelete = jest.fn()
  const mockOnView = jest.fn()

  const defaultProps = {
    onEdit: mockOnEdit,
    onDelete: mockOnDelete,
    onView: mockOnView,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders customer information correctly', () => {
    const customer = createMockCustomer({
      id: 1,
      name: 'John Doe',
      short_title: 'Mr. John',
      location: 'Downtown',
      address: '123 Main St',
      star_rating: 4,
      is_new_customer: false,
    })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    expect(screen.getByTestId('customer-name')).toHaveTextContent('John Doe')
    expect(screen.getByTestId('customer-title')).toHaveTextContent('Mr. John')
    expect(screen.getByTestId('customer-location')).toHaveTextContent('📍 Downtown')
    expect(screen.getByTestId('customer-address')).toHaveTextContent('123 Main St')
  })

  it('displays new customer badge when customer is new', () => {
    const newCustomer = createMockCustomer({
      is_new_customer: true,
    })

    render(<MockCustomerCard customer={newCustomer} {...defaultProps} />)

    expect(screen.getByTestId('new-customer-badge')).toHaveTextContent('New')
  })

  it('does not display new customer badge for existing customers', () => {
    const existingCustomer = createMockCustomer({
      is_new_customer: false,
    })

    render(<MockCustomerCard customer={existingCustomer} {...defaultProps} />)

    expect(screen.queryByTestId('new-customer-badge')).not.toBeInTheDocument()
  })

  it('displays correct star rating', () => {
    const customer = createMockCustomer({
      star_rating: 3,
    })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    // Check that 3 stars are filled
    expect(screen.getByTestId('star-1')).toHaveClass('star-filled')
    expect(screen.getByTestId('star-2')).toHaveClass('star-filled')
    expect(screen.getByTestId('star-3')).toHaveClass('star-filled')
    expect(screen.getByTestId('star-4')).toHaveClass('star-empty')
    expect(screen.getByTestId('star-5')).toHaveClass('star-empty')
  })

  it('displays priority badge when customer has priority', () => {
    const priorityCustomer = createMockCustomer({
      priority: 2,
    })

    render(<MockCustomerCard customer={priorityCustomer} {...defaultProps} />)

    expect(screen.getByTestId('priority-badge')).toHaveTextContent('Priority: 2')
  })

  it('does not display priority badge when priority is 0', () => {
    const noPriorityCustomer = createMockCustomer({
      priority: 0,
    })

    render(<MockCustomerCard customer={noPriorityCustomer} {...defaultProps} />)

    expect(screen.queryByTestId('priority-badge')).not.toBeInTheDocument()
  })

  it('displays EMI information correctly', () => {
    const customer = createMockCustomer({
      emi_type: 'weekly',
      next_emi_due_date: '2024-01-15',
    })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    expect(screen.getByTestId('customer-emi')).toHaveTextContent(
      'EMI: weekly - Due: 2024-01-15'
    )
  })

  it('calls onView when view button is clicked', async () => {
    const user = userEvent.setup()
    const customer = createMockCustomer({ id: 1 })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    await user.click(screen.getByTestId('view-customer-button'))

    expect(mockOnView).toHaveBeenCalledWith(1)
  })

  it('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup()
    const customer = createMockCustomer({ id: 2 })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    await user.click(screen.getByTestId('edit-customer-button'))

    expect(mockOnEdit).toHaveBeenCalledWith(2)
  })

  it('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup()
    const customer = createMockCustomer({ id: 3 })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    await user.click(screen.getByTestId('delete-customer-button'))

    expect(mockOnDelete).toHaveBeenCalledWith(3)
  })

  it('renders customer avatar with correct src and alt', () => {
    const customer = createMockCustomer({
      name: 'Jane Smith',
      profile_photo: 'jane-profile.jpg',
    })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    const avatar = screen.getByTestId('customer-avatar')
    expect(avatar).toHaveAttribute('src', 'jane-profile.jpg')
    expect(avatar).toHaveAttribute('alt', 'Jane Smith profile')
  })

  it('handles missing optional callbacks gracefully', async () => {
    const user = userEvent.setup()
    const customer = createMockCustomer({ id: 1 })

    render(<MockCustomerCard customer={customer} />)

    // Should not throw errors when callbacks are not provided
    await user.click(screen.getByTestId('view-customer-button'))
    await user.click(screen.getByTestId('edit-customer-button'))
    await user.click(screen.getByTestId('delete-customer-button'))

    // No assertions needed - just ensuring no errors are thrown
  })

  it('applies correct CSS classes', () => {
    const customer = createMockCustomer({ id: 1 })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    expect(screen.getByTestId('customer-card-1')).toHaveClass('customer-card')
    expect(screen.getByTestId('view-customer-button')).toHaveClass('action-button', 'view')
    expect(screen.getByTestId('edit-customer-button')).toHaveClass('action-button', 'edit')
    expect(screen.getByTestId('delete-customer-button')).toHaveClass('action-button', 'delete')
  })

  it('handles long customer names and addresses gracefully', () => {
    const customer = createMockCustomer({
      name: 'Very Long Customer Name That Might Overflow',
      address: 'Very Long Address That Might Cause Layout Issues 123 Main Street Apartment 456',
    })

    render(<MockCustomerCard customer={customer} {...defaultProps} />)

    expect(screen.getByTestId('customer-name')).toHaveTextContent(
      'Very Long Customer Name That Might Overflow'
    )
    expect(screen.getByTestId('customer-address')).toHaveTextContent(
      'Very Long Address That Might Cause Layout Issues 123 Main Street Apartment 456'
    )
  })

  it('handles edge case star ratings', () => {
    // Test 0 stars
    const zeroStarCustomer = createMockCustomer({ star_rating: 0 })
    const { rerender } = render(<MockCustomerCard customer={zeroStarCustomer} {...defaultProps} />)

    Array.from({ length: 5 }, (_, index) => {
      expect(screen.getByTestId(`star-${index + 1}`)).toHaveClass('star-empty')
    })

    // Test 5 stars
    const fiveStarCustomer = createMockCustomer({ star_rating: 5 })
    rerender(<MockCustomerCard customer={fiveStarCustomer} {...defaultProps} />)

    Array.from({ length: 5 }, (_, index) => {
      expect(screen.getByTestId(`star-${index + 1}`)).toHaveClass('star-filled')
    })
  })
})
