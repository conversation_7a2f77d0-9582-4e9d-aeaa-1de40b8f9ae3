import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import RouteCreate from '@/app/pages/route/components/routeCreate/RouteCreate'
import { mockRouteService, mockBranchList, mockAgentList, resetRouteServiceMocks } from '../../mocks/route-service.mock'

// Mock the route service
jest.mock('@/app/pages/route/route-service', () => mockRouteService)

// Mock contexts
const mockSetIsLoading = jest.fn()
const mockFire = jest.fn()

jest.mock('@/app/contexts/commonContext', () => ({
  useCommonContext: () => ({
    setIsLoading: mockSetIsLoading,
  }),
}))

jest.mock('@/app/components/utilities/Alert/Alert', () => ({
  useAlert: () => ({
    fire: mockFire,
  }),
}))

// Mock react-hook-form
const mockSetValue = jest.fn()
const mockGetValues = jest.fn()
const mockHandleSubmit = jest.fn()
const mockRegister = jest.fn()
const mockReset = jest.fn()

jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: mockHandleSubmit,
    formState: { errors: {} },
    setValue: mockSetValue,
    getValues: mockGetValues,
    register: mockRegister,
    reset: mockReset,
  }),
  Controller: ({ render }: any) => render({ field: {} }),
  SubmitHandler: jest.fn(),
}))

const mockProps = {
  showCreate: true,
  handleCloseCreate: jest.fn(),
}

describe('RouteCreate Component', () => {
  beforeEach(() => {
    resetRouteServiceMocks()
    jest.clearAllMocks()
    
    // Setup default mock returns
    mockRouteService.fetchBranchList.mockResolvedValue({ results: mockBranchList })
    mockRouteService.fetchAgentList.mockResolvedValue({ results: mockAgentList })
    mockRouteService.createRoute.mockResolvedValue({ data: { success: true } })
  })

  it('renders create route form when showCreate is true', async () => {
    render(<RouteCreate {...mockProps} />)
    
    expect(screen.getByText('Create Route')).toBeInTheDocument()
    expect(screen.getByLabelText('Route Title')).toBeInTheDocument()
    expect(screen.getByLabelText('Day')).toBeInTheDocument()
    expect(screen.getByLabelText('Select Agent')).toBeInTheDocument()
    expect(screen.getByLabelText('Select Branch')).toBeInTheDocument()
  })

  it('does not render when showCreate is false', () => {
    render(<RouteCreate {...mockProps} showCreate={false} />)
    
    expect(screen.queryByText('Create Route')).not.toBeInTheDocument()
  })

  it('loads branch and agent lists on mount', async () => {
    render(<RouteCreate {...mockProps} />)
    
    await waitFor(() => {
      expect(mockRouteService.fetchBranchList).toHaveBeenCalled()
      expect(mockRouteService.fetchAgentList).toHaveBeenCalled()
    })
  })

  it('handles form submission successfully', async () => {
    const user = userEvent.setup()
    
    mockHandleSubmit.mockImplementation((callback) => (e) => {
      e.preventDefault()
      callback({
        route_name: 'New Test Route',
        day: 'Monday',
        agent_id: 1,
        branch_id: 1,
      })
    })

    render(<RouteCreate {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Create Route')).toBeInTheDocument()
    })

    const submitButton = screen.getByText('Create')
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockRouteService.createRoute).toHaveBeenCalledWith({
        route_name: 'New Test Route',
        day: 'Monday',
        agent_id: 1,
        branch_id: 1,
      })
    })

    expect(mockFire).toHaveBeenCalledWith(
      expect.objectContaining({
        position: 'top-right',
        icon: 'success',
        title: 'Created Successfully',
        autoClose: 2000,
      })
    )
  })

  it('handles form submission errors', async () => {
    const user = userEvent.setup()
    
    mockRouteService.createRoute.mockRejectedValue({
      response: {
        data: { detail: 'Route name already exists' },
      },
    })

    mockHandleSubmit.mockImplementation((callback) => (e) => {
      e.preventDefault()
      callback({
        route_name: 'Duplicate Route',
        day: 'Monday',
        agent_id: 1,
        branch_id: 1,
      })
    })

    render(<RouteCreate {...mockProps} />)
    
    const submitButton = screen.getByText('Create')
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockFire).toHaveBeenCalledWith(
        expect.objectContaining({
          position: 'center',
          icon: 'error',
          title: 'Some error occurred',
          text: 'Route name already exists',
        })
      )
    })
  })

  it('handles cancel action', async () => {
    const user = userEvent.setup()
    render(<RouteCreate {...mockProps} />)
    
    const cancelButton = screen.getByText('Cancel')
    await user.click(cancelButton)

    expect(mockProps.handleCloseCreate).toHaveBeenCalled()
    expect(mockReset).toHaveBeenCalled()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    
    // Mock form with validation errors
    const mockFormWithErrors = {
      control: {},
      handleSubmit: mockHandleSubmit,
      formState: { 
        errors: {
          route_name: { message: 'Route title is required' },
          day: { message: 'Day is required' },
          agent_id: { message: 'Agent is required' },
          branch_id: { message: 'Branch is required' },
        }
      },
      setValue: mockSetValue,
      getValues: mockGetValues,
      register: mockRegister,
      reset: mockReset,
    }

    jest.mocked(require('react-hook-form').useForm).mockReturnValue(mockFormWithErrors)

    render(<RouteCreate {...mockProps} />)
    
    expect(screen.getByText('Route title is required')).toBeInTheDocument()
    expect(screen.getByText('Day is required')).toBeInTheDocument()
    expect(screen.getByText('Agent is required')).toBeInTheDocument()
    expect(screen.getByText('Branch is required')).toBeInTheDocument()
  })

  it('displays day options correctly', async () => {
    render(<RouteCreate {...mockProps} />)
    
    const daySelect = screen.getByLabelText('Day')
    expect(daySelect).toBeInTheDocument()
    
    // Check if all days are available as options
    const options = screen.getAllByRole('option')
    const dayOptions = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    
    dayOptions.forEach(day => {
      expect(screen.getByDisplayValue(day) || screen.getByText(day)).toBeInTheDocument()
    })
  })

  it('handles agent selection', async () => {
    const user = userEvent.setup()
    render(<RouteCreate {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByLabelText('Select Agent')).toBeInTheDocument()
    })

    // Test agent autocomplete interaction
    const agentField = screen.getByLabelText('Select Agent')
    await user.click(agentField)
    
    // Verify that setValue is called when agent is selected
    expect(mockSetValue).toHaveBeenCalled()
  })

  it('handles branch selection', async () => {
    const user = userEvent.setup()
    render(<RouteCreate {...mockProps} />)
    
    await waitFor(() => {
      expect(screen.getByLabelText('Select Branch')).toBeInTheDocument()
    })

    // Test branch autocomplete interaction
    const branchField = screen.getByLabelText('Select Branch')
    await user.click(branchField)
    
    // Verify that setValue is called when branch is selected
    expect(mockSetValue).toHaveBeenCalled()
  })

  it('handles API errors when loading data', async () => {
    mockRouteService.fetchBranchList.mockRejectedValue(new Error('Failed to load branches'))
    mockRouteService.fetchAgentList.mockRejectedValue(new Error('Failed to load agents'))
    
    render(<RouteCreate {...mockProps} />)
    
    await waitFor(() => {
      // Component should handle errors gracefully
      expect(mockRouteService.fetchBranchList).toHaveBeenCalled()
      expect(mockRouteService.fetchAgentList).toHaveBeenCalled()
    })
  })

  it('resets form when component unmounts or closes', async () => {
    const { unmount } = render(<RouteCreate {...mockProps} />)
    
    unmount()
    
    // Form should be reset when component is closed
    expect(mockReset).toHaveBeenCalled()
  })

  it('shows loading state during form submission', async () => {
    const user = userEvent.setup()
    
    mockHandleSubmit.mockImplementation((callback) => (e) => {
      e.preventDefault()
      callback({
        route_name: 'Test Route',
        day: 'Monday',
        agent_id: 1,
        branch_id: 1,
      })
    })

    render(<RouteCreate {...mockProps} />)
    
    const submitButton = screen.getByText('Create')
    await user.click(submitButton)

    expect(mockSetIsLoading).toHaveBeenCalledWith(true)
  })
})
