import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import RouteListPage from '@/app/pages/route/page'
import { mockRouteService, resetRouteServiceMocks } from '../../mocks/route-service.mock'

// Mock the route service
jest.mock('@/app/pages/route/route-service', () => mockRouteService)

// Mock contexts
const mockSetIsLoading = jest.fn()
const mockFire = jest.fn()

jest.mock('@/app/contexts/commonContext', () => ({
  useCommonContext: () => ({
    setIsLoading: mockSetIsLoading,
    screenSize: 1920,
  }),
}))

jest.mock('@/app/components/utilities/Alert/Alert', () => ({
  useAlert: () => ({
    fire: mockFire,
  }),
}))

// Mock components
jest.mock('@/app/components/Shimmer/PaginationWithShimmer/PaginationWithShimmer', () => {
  return function MockTableWithShimmer({ children }: { children: React.ReactNode }) {
    return <div data-testid="table-shimmer">{children}</div>
  }
})

jest.mock('@/app/components/utilities/Pagination/Pagination', () => {
  return function MockPagination(props: any) {
    return <div data-testid="pagination" onClick={() => props.onPageChange?.(2)}>Pagination</div>
  }
})

jest.mock('@/app/components/TableMenuTwo/TableMenuTwo', () => {
  return function MockTableMenuTwo(props: any) {
    return (
      <div data-testid="table-menu">
        <button onClick={() => props.onEdit?.(1)}>Edit</button>
        <button onClick={() => props.onDelete?.(1)}>Delete</button>
      </div>
    )
  }
})

jest.mock('./components/routeCreate/RouteCreate', () => {
  return function MockRouteCreate(props: any) {
    return (
      <div data-testid="route-create">
        {props.showCreate && (
          <div>
            <button onClick={props.handleCloseCreate}>Close Create</button>
          </div>
        )}
      </div>
    )
  }
})

const mockRouteList = [
  {
    id: 1,
    route_name: 'Route 1',
    day: 'Monday',
    agent_name: 'Agent 1',
    branch_name: 'Branch 1',
    created_by_user_name: 'Admin',
    created_at: '2024-01-01T10:00:00Z',
    agent_profile_photo: 'agent1.jpg',
    created_by_usertype: 'admin',
    page: 1,
    agent_id: 1,
  },
  {
    id: 2,
    route_name: 'Route 2',
    day: 'Tuesday',
    agent_name: 'Agent 2',
    branch_name: 'Branch 2',
    created_by_user_name: 'Manager',
    created_at: '2024-01-02T10:00:00Z',
    agent_profile_photo: 'agent2.jpg',
    created_by_usertype: 'manager',
    page: 1,
    agent_id: 2,
  },
]

describe('RouteListPage', () => {
  beforeEach(() => {
    resetRouteServiceMocks()
    jest.clearAllMocks()
    
    // Setup default mock returns
    mockRouteService.getRouteList.mockResolvedValue({
      results: mockRouteList,
      total_pages: 1,
      total: 2,
      page: 1,
      page_size: 10,
      links: { next: '', previous: '' },
    })
  })

  it('renders route list page correctly', async () => {
    render(<RouteListPage />)
    
    expect(screen.getByText('Routes')).toBeInTheDocument()
    expect(screen.getByText('+ Create Route')).toBeInTheDocument()
  })

  it('loads and displays route list', async () => {
    render(<RouteListPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Route 1')).toBeInTheDocument()
      expect(screen.getByText('Route 2')).toBeInTheDocument()
      expect(screen.getByText('Agent 1')).toBeInTheDocument()
      expect(screen.getByText('Agent 2')).toBeInTheDocument()
    })
  })

  it('handles search functionality', async () => {
    const user = userEvent.setup()
    render(<RouteListPage />)
    
    const searchInput = screen.getByPlaceholderText('Search...')
    await user.type(searchInput, 'Route 1')
    
    await waitFor(() => {
      expect(mockRouteService.getRouteList).toHaveBeenCalledWith(
        0, 10, 'Route 1', ''
      )
    })
  })

  it('handles day filter', async () => {
    const user = userEvent.setup()
    render(<RouteListPage />)
    
    // Find and interact with day filter
    const dayFilter = screen.getByDisplayValue('All Days')
    await user.selectOptions(dayFilter, 'Monday')
    
    await waitFor(() => {
      expect(mockRouteService.getRouteList).toHaveBeenCalledWith(
        0, 10, '', 'Monday'
      )
    })
  })

  it('opens create route modal', async () => {
    const user = userEvent.setup()
    render(<RouteListPage />)
    
    const createButton = screen.getByText('+ Create Route')
    await user.click(createButton)
    
    expect(screen.getByTestId('route-create')).toBeInTheDocument()
  })

  it('handles route deletion', async () => {
    const user = userEvent.setup()
    mockRouteService.deleteRoute.mockResolvedValue({ data: { success: true } })
    
    render(<RouteListPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Route 1')).toBeInTheDocument()
    })

    const deleteButton = screen.getByText('Delete')
    await user.click(deleteButton)

    expect(mockFire).toHaveBeenCalledWith(
      expect.objectContaining({
        position: 'center',
        icon: 'info',
        title: 'Are You Sure',
        text: 'Do you want to delete this route?',
      })
    )
  })

  it('handles pagination', async () => {
    const user = userEvent.setup()
    render(<RouteListPage />)
    
    const pagination = screen.getByTestId('pagination')
    await user.click(pagination)
    
    await waitFor(() => {
      expect(mockRouteService.getRouteList).toHaveBeenCalledWith(
        10, 10, '', ''
      )
    })
  })

  it('displays loading state', async () => {
    render(<RouteListPage />)
    
    expect(screen.getByTestId('table-shimmer')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    mockRouteService.getRouteList.mockRejectedValue(new Error('API Error'))
    
    render(<RouteListPage />)
    
    await waitFor(() => {
      // The component should handle the error gracefully
      expect(mockSetIsLoading).toHaveBeenCalledWith(false)
    })
  })

  it('formats dates correctly', async () => {
    render(<RouteListPage />)
    
    await waitFor(() => {
      // Check if date formatting is applied
      expect(screen.getByText('Route 1')).toBeInTheDocument()
    })
  })

  it('displays agent profile photos', async () => {
    render(<RouteListPage />)
    
    await waitFor(() => {
      const images = screen.getAllByRole('img')
      expect(images.length).toBeGreaterThan(0)
    })
  })

  it('handles empty route list', async () => {
    mockRouteService.getRouteList.mockResolvedValue({
      results: [],
      total_pages: 0,
      total: 0,
      page: 1,
      page_size: 10,
      links: { next: '', previous: '' },
    })
    
    render(<RouteListPage />)
    
    await waitFor(() => {
      expect(screen.getByText('No routes found')).toBeInTheDocument()
    })
  })

  it('debounces search input', async () => {
    const user = userEvent.setup()
    render(<RouteListPage />)
    
    const searchInput = screen.getByPlaceholderText('Search...')
    
    // Type multiple characters quickly
    await user.type(searchInput, 'test')
    
    // Should only call the API once after debounce
    await waitFor(() => {
      expect(mockRouteService.getRouteList).toHaveBeenCalledTimes(2) // Initial load + debounced search
    })
  })

  it('handles filter toggle', async () => {
    const user = userEvent.setup()
    render(<RouteListPage />)
    
    const filterButton = screen.getByText('Filter')
    await user.click(filterButton)
    
    // Should toggle filter visibility
    expect(screen.getByTestId('filter-panel')).toBeInTheDocument()
  })
})
