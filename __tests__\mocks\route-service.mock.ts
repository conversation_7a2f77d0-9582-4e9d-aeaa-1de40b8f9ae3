import { 
  route_details, 
  customerList, 
  routeList, 
  routeEdit,
  create_route 
} from '@/app/pages/route/route.model'
import { Branch_List_Item } from '@/app/pages/branch/branch.model'
import { Agent_List_Item } from '@/app/pages/agent/agent.model'

// Mock data
export const mockRouteDetails: route_details = {
  route_name: 'Test Route',
  day: 'Monday',
  created_by: 1,
  branch_id: 1,
  agent_id: 1,
  route_customers: [
    {
      id: 1,
      name: '<PERSON>',
      short_title: 'Mr. <PERSON>',
      priority: 1,
      profile_photo: 'profile1.jpg',
      location: 'Location 1',
      address: 'Address 1',
      is_new_customer: false,
      emi_type: 'monthly',
      emi_period_type: 'monthly',
      next_emi_due_date: '2024-01-15',
      star_rating: 4,
      branch_id: 1,
    },
    {
      id: 2,
      name: '<PERSON>',
      short_title: 'Ms. <PERSON>',
      priority: 2,
      profile_photo: 'profile2.jpg',
      location: 'Location 2',
      address: 'Address 2',
      is_new_customer: true,
      emi_type: 'weekly',
      emi_period_type: 'weekly',
      next_emi_due_date: '2024-01-20',
      star_rating: 5,
      branch_id: 1,
    },
  ],
}

export const mockCustomerList: customerList = {
  links: {
    next: '',
    previous: '',
  },
  total: 3,
  page: 1,
  page_size: 10,
  total_pages: 1,
  results: [
    {
      id: 3,
      name: 'Bob Johnson',
      short_title: 'Mr. Bob',
      priority: 0,
      profile_photo: 'profile3.jpg',
      location: 'Location 3',
      address: 'Address 3',
      is_new_customer: false,
      emi_type: 'monthly',
      emi_period_type: 'monthly',
      next_emi_due_date: '2024-01-25',
      star_rating: 3,
      branch_id: 1,
    },
    {
      id: 4,
      name: 'Alice Brown',
      short_title: 'Ms. Alice',
      priority: 0,
      profile_photo: 'profile4.jpg',
      location: 'Location 4',
      address: 'Address 4',
      is_new_customer: true,
      emi_type: 'weekly',
      emi_period_type: 'weekly',
      next_emi_due_date: '2024-01-30',
      star_rating: 4,
      branch_id: 2,
    },
  ],
}

export const mockBranchList: Branch_List_Item[] = [
  { id: 1, name: 'Branch 1' },
  { id: 2, name: 'Branch 2' },
  { id: 3, name: 'Branch 3' },
]

export const mockAgentList: Agent_List_Item[] = [
  { id: 1, name: 'Agent 1' },
  { id: 2, name: 'Agent 2' },
  { id: 3, name: 'Agent 3' },
]

// Mock service functions
export const mockRouteService = {
  getRouteDetails: jest.fn().mockResolvedValue(mockRouteDetails),
  fetchCustomerList: jest.fn().mockResolvedValue(mockCustomerList),
  getBrachList: jest.fn().mockResolvedValue({ results: mockBranchList }),
  getAgentsList: jest.fn().mockResolvedValue({ results: mockAgentList }),
  editRoute: jest.fn().mockResolvedValue({ data: { success: true } }),
  createRoute: jest.fn().mockResolvedValue({ data: { success: true } }),
  deleteRoute: jest.fn().mockResolvedValue({ data: { success: true } }),
  getRouteList: jest.fn().mockResolvedValue({
    results: [],
    total_pages: 1,
    total: 0,
    page: 1,
    page_size: 10,
    links: { next: '', previous: '' },
  }),
}

// Mock axios responses
export const mockAxiosResponses = {
  getRouteDetails: {
    data: mockRouteDetails,
    status: 200,
    statusText: 'OK',
  },
  fetchCustomerList: {
    data: mockCustomerList,
    status: 200,
    statusText: 'OK',
  },
  getBranchList: {
    data: { results: mockBranchList },
    status: 200,
    statusText: 'OK',
  },
  getAgentsList: {
    data: { results: mockAgentList },
    status: 200,
    statusText: 'OK',
  },
  editRoute: {
    data: { success: true },
    status: 200,
    statusText: 'OK',
  },
  error: {
    response: {
      data: { detail: 'Test error message' },
      status: 500,
      statusText: 'Internal Server Error',
    },
    message: 'Network Error',
  },
}

// Jest mock setup for route service
export const setupRouteServiceMocks = () => {
  jest.mock('@/app/pages/route/route-service', () => mockRouteService)
  jest.mock('@/app/pages/agent/agent-service', () => ({
    getBrachList: mockRouteService.getBrachList,
  }))
}

// Reset all mocks
export const resetRouteServiceMocks = () => {
  Object.values(mockRouteService).forEach(mock => {
    if (jest.isMockFunction(mock)) {
      mock.mockClear()
    }
  })
}
