import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// Mock the OrderCreate component with the validateRows function
const mockValidateRows = jest.fn()
const mockSetRowErrors = jest.fn()

// Mock component that includes the validateRows function
const MockOrderCreate = () => {
  const [rows, setRows] = React.useState([])
  const [rowErrors, setRowErrors] = React.useState([])

  const validateRows = () => {
    if (rows.length === 0) {
      // If no rows are added, return an error
      setRowErrors([
        { itemName: "At least one item is required", quantity: "", amount: "" },
      ])
      return false
    }

    const newErrors = rows.map((row) => ({
      itemName: !row.itemName ? "Item Name is required" : "",
      quantity: !row.quantity ? "Quantity is required" : "",
      amount: !row.amount ? "Amount is required" : "",
    }))
    setRowErrors(newErrors)

    // Return whether all rows are valid
    return newErrors.every(
      (error) => !error.itemName && !error.quantity && !error.amount
    )
  }

  return (
    <div>
      <button onClick={validateRows} data-testid="validate-button">
        Validate Rows
      </button>
      <div data-testid="rows-count">{rows.length}</div>
      <div data-testid="errors-count">{rowErrors.length}</div>
      {rowErrors.map((error, index) => (
        <div key={index} data-testid={`error-${index}`}>
          <span data-testid={`error-itemName-${index}`}>{error.itemName}</span>
          <span data-testid={`error-quantity-${index}`}>{error.quantity}</span>
          <span data-testid={`error-amount-${index}`}>{error.amount}</span>
        </div>
      ))}
      <button 
        onClick={() => setRows([
          { itemName: 'Test Item', quantity: '5', amount: '100' }
        ])}
        data-testid="add-valid-row"
      >
        Add Valid Row
      </button>
      <button 
        onClick={() => setRows([
          { itemName: '', quantity: '', amount: '' }
        ])}
        data-testid="add-invalid-row"
      >
        Add Invalid Row
      </button>
      <button 
        onClick={() => setRows([
          { itemName: 'Item 1', quantity: '2', amount: '50' },
          { itemName: '', quantity: '3', amount: '' }
        ])}
        data-testid="add-mixed-rows"
      >
        Add Mixed Rows
      </button>
    </div>
  )
}

describe('OrderCreate - validateRows function', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return false and set error when no rows are present', async () => {
    const user = userEvent.setup()
    render(<MockOrderCreate />)
    
    // Initially no rows
    expect(screen.getByTestId('rows-count')).toHaveTextContent('0')
    
    // Validate with no rows
    await user.click(screen.getByTestId('validate-button'))
    
    // Should show error for no items
    await waitFor(() => {
      expect(screen.getByTestId('error-itemName-0')).toHaveTextContent('At least one item is required')
      expect(screen.getByTestId('errors-count')).toHaveTextContent('1')
    })
  })

  it('should return true when all rows are valid', async () => {
    const user = userEvent.setup()
    render(<MockOrderCreate />)
    
    // Add a valid row
    await user.click(screen.getByTestId('add-valid-row'))
    expect(screen.getByTestId('rows-count')).toHaveTextContent('1')
    
    // Validate rows
    await user.click(screen.getByTestId('validate-button'))
    
    // Should have no errors (validation passed)
    await waitFor(() => {
      expect(screen.getByTestId('errors-count')).toHaveTextContent('1') // One row, no errors
    })
  })

  it('should return false and set errors for invalid rows', async () => {
    const user = userEvent.setup()
    render(<MockOrderCreate />)
    
    // Add an invalid row (empty fields)
    await user.click(screen.getByTestId('add-invalid-row'))
    expect(screen.getByTestId('rows-count')).toHaveTextContent('1')
    
    // Validate rows
    await user.click(screen.getByTestId('validate-button'))
    
    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByTestId('error-itemName-0')).toHaveTextContent('Item Name is required')
      expect(screen.getByTestId('error-quantity-0')).toHaveTextContent('Quantity is required')
      expect(screen.getByTestId('error-amount-0')).toHaveTextContent('Amount is required')
    })
  })

  it('should validate multiple rows correctly', async () => {
    const user = userEvent.setup()
    render(<MockOrderCreate />)
    
    // Add mixed valid/invalid rows
    await user.click(screen.getByTestId('add-mixed-rows'))
    expect(screen.getByTestId('rows-count')).toHaveTextContent('2')
    
    // Validate rows
    await user.click(screen.getByTestId('validate-button'))
    
    // Should show errors for the invalid row only
    await waitFor(() => {
      expect(screen.getByTestId('errors-count')).toHaveTextContent('2')
      
      // First row should have no errors (all fields empty in error display means no errors)
      expect(screen.getByTestId('error-itemName-0')).toHaveTextContent('')
      expect(screen.getByTestId('error-quantity-0')).toHaveTextContent('')
      expect(screen.getByTestId('error-amount-0')).toHaveTextContent('')
      
      // Second row should have errors
      expect(screen.getByTestId('error-itemName-1')).toHaveTextContent('Item Name is required')
      expect(screen.getByTestId('error-quantity-1')).toHaveTextContent('')
      expect(screen.getByTestId('error-amount-1')).toHaveTextContent('Amount is required')
    })
  })

  it('should handle edge cases correctly', async () => {
    const user = userEvent.setup()
    render(<MockOrderCreate />)
    
    // Test with whitespace-only values
    const MockOrderCreateWithWhitespace = () => {
      const [rows, setRows] = React.useState([
        { itemName: '   ', quantity: '0', amount: '0' }
      ])
      const [rowErrors, setRowErrors] = React.useState([])

      const validateRows = () => {
        if (rows.length === 0) {
          setRowErrors([
            { itemName: "At least one item is required", quantity: "", amount: "" },
          ])
          return false
        }

        const newErrors = rows.map((row) => ({
          itemName: !row.itemName || !row.itemName.trim() ? "Item Name is required" : "",
          quantity: !row.quantity || row.quantity === '0' ? "Quantity is required" : "",
          amount: !row.amount || row.amount === '0' ? "Amount is required" : "",
        }))
        setRowErrors(newErrors)

        return newErrors.every(
          (error) => !error.itemName && !error.quantity && !error.amount
        )
      }

      return (
        <div>
          <button onClick={validateRows} data-testid="validate-whitespace">
            Validate Whitespace
          </button>
          {rowErrors.map((error, index) => (
            <div key={index} data-testid={`whitespace-error-${index}`}>
              <span data-testid={`whitespace-error-itemName-${index}`}>{error.itemName}</span>
              <span data-testid={`whitespace-error-quantity-${index}`}>{error.quantity}</span>
              <span data-testid={`whitespace-error-amount-${index}`}>{error.amount}</span>
            </div>
          ))}
        </div>
      )
    }

    const { rerender } = render(<MockOrderCreateWithWhitespace />)
    
    await user.click(screen.getByTestId('validate-whitespace'))
    
    // Should handle whitespace and zero values appropriately
    await waitFor(() => {
      expect(screen.getByTestId('whitespace-error-itemName-0')).toHaveTextContent('Item Name is required')
      expect(screen.getByTestId('whitespace-error-quantity-0')).toHaveTextContent('Quantity is required')
      expect(screen.getByTestId('whitespace-error-amount-0')).toHaveTextContent('Amount is required')
    })
  })
})

// Unit tests for the validateRows function in isolation
describe('validateRows function - Unit Tests', () => {
  let mockSetRowErrors
  let validateRows

  beforeEach(() => {
    mockSetRowErrors = jest.fn()
    
    // Create the function in isolation
    validateRows = (rows) => {
      if (rows.length === 0) {
        mockSetRowErrors([
          { itemName: "At least one item is required", quantity: "", amount: "" },
        ])
        return false
      }

      const newErrors = rows.map((row) => ({
        itemName: !row.itemName ? "Item Name is required" : "",
        quantity: !row.quantity ? "Quantity is required" : "",
        amount: !row.amount ? "Amount is required" : "",
      }))
      mockSetRowErrors(newErrors)

      return newErrors.every(
        (error) => !error.itemName && !error.quantity && !error.amount
      )
    }
  })

  it('should return false for empty rows array', () => {
    const result = validateRows([])
    
    expect(result).toBe(false)
    expect(mockSetRowErrors).toHaveBeenCalledWith([
      { itemName: "At least one item is required", quantity: "", amount: "" }
    ])
  })

  it('should return true for valid rows', () => {
    const validRows = [
      { itemName: 'Item 1', quantity: '5', amount: '100' },
      { itemName: 'Item 2', quantity: '3', amount: '75' }
    ]
    
    const result = validateRows(validRows)
    
    expect(result).toBe(true)
    expect(mockSetRowErrors).toHaveBeenCalledWith([
      { itemName: "", quantity: "", amount: "" },
      { itemName: "", quantity: "", amount: "" }
    ])
  })

  it('should return false for invalid rows', () => {
    const invalidRows = [
      { itemName: '', quantity: '5', amount: '100' },
      { itemName: 'Item 2', quantity: '', amount: '75' }
    ]
    
    const result = validateRows(invalidRows)
    
    expect(result).toBe(false)
    expect(mockSetRowErrors).toHaveBeenCalledWith([
      { itemName: "Item Name is required", quantity: "", amount: "" },
      { itemName: "", quantity: "Quantity is required", amount: "" }
    ])
  })

  it('should handle mixed valid and invalid rows', () => {
    const mixedRows = [
      { itemName: 'Valid Item', quantity: '5', amount: '100' },
      { itemName: '', quantity: '', amount: '' }
    ]
    
    const result = validateRows(mixedRows)
    
    expect(result).toBe(false)
    expect(mockSetRowErrors).toHaveBeenCalledWith([
      { itemName: "", quantity: "", amount: "" },
      { itemName: "Item Name is required", quantity: "Quantity is required", amount: "Amount is required" }
    ])
  })
})
