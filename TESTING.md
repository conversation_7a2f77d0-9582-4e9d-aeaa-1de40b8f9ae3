# Testing Guide for EcoGO Choice EMI Dashboard

## Overview
This project uses Je<PERSON> with React Testing Library for comprehensive testing of the Next.js application. The testing setup includes unit tests, component tests, integration tests, and service layer tests.

## Quick Start

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run tests for CI/CD (no watch mode)
npm run test:ci

# Run specific test file
npm test -- __tests__/setup.test.ts

# Run tests matching a pattern
npm test -- --testNamePattern="should handle"
```

## Test Structure

### Directory Organization
```
__tests__/
├── components/          # Component-specific tests
│   ├── route/
│   │   └── RouteCreate.test.tsx
│   └── purchaseOrder/
│       └── OrderCreate.test.tsx
├── pages/              # Page component tests
│   └── route/
│       ├── routeDetails.test.tsx
│       └── routeList.test.tsx
├── services/           # API service tests
│   └── route-service.test.ts
├── integration/        # Cross-component workflow tests
│   └── route-workflow.test.tsx
├── utils/              # Test utilities and helpers
│   └── test-utils.tsx
├── mocks/              # Mock data and services
│   └── route-service.mock.ts
└── setup.test.ts       # Basic setup verification
```

## Test Categories

### 1. Unit Tests
**Purpose**: Test individual functions and methods in isolation
**Location**: `__tests__/services/`
**Example**: API service functions, utility functions

```typescript
describe('getRouteDetails', () => {
  it('should fetch route details successfully', async () => {
    mockAxiosInstance.get.mockResolvedValue(mockResponse)
    const result = await getRouteDetails(1)
    expect(result).toEqual(expectedData)
  })
})
```

### 2. Component Tests
**Purpose**: Test React components with user interactions
**Location**: `__tests__/components/`
**Example**: Form submissions, button clicks, state changes

```typescript
it('handles form submission successfully', async () => {
  render(<RouteCreate {...props} />)
  await user.click(screen.getByText('Create'))
  expect(mockCreateRoute).toHaveBeenCalled()
})
```

### 3. Page Tests
**Purpose**: Test full page components with complex interactions
**Location**: `__tests__/pages/`
**Example**: Data loading, navigation, error handling

### 4. Integration Tests
**Purpose**: Test complete user workflows
**Location**: `__tests__/integration/`
**Example**: End-to-end scenarios across multiple components

## Configuration Files

### jest.config.js
- Uses Next.js Jest configuration
- Sets up jsdom environment
- Configures module name mapping for `@/` imports
- Sets coverage thresholds (70% minimum)

### jest.setup.js
- Imports jest-dom matchers
- Mocks Next.js components (router, Image)
- Sets up global mocks (window.matchMedia, etc.)
- Configures environment variables

## Mock Strategy

### Service Mocks
Located in `__tests__/mocks/route-service.mock.ts`:
- Predefined mock data for consistent testing
- Configurable API responses
- Error simulation capabilities
- Reset utilities for clean test state

### Component Mocks
- Next.js router: Mocked with jest functions
- Next.js Image: Replaced with simple img element
- Material-UI components: Simplified versions
- react-beautiful-dnd: Basic mock implementation

## Writing Tests

### Best Practices

1. **Descriptive Test Names**
```typescript
// Good
it('should display error message when API call fails')

// Avoid
it('test error')
```

2. **Arrange-Act-Assert Pattern**
```typescript
it('should handle customer deletion', async () => {
  // Arrange
  render(<RouteDetails />)
  await waitFor(() => screen.getByText('John Doe'))
  
  // Act
  await user.click(screen.getByText('delete'))
  
  // Assert
  expect(mockFire).toHaveBeenCalledWith(
    expect.objectContaining({ title: 'Are You Sure' })
  )
})
```

3. **User-Centric Testing**
```typescript
// Good - test from user perspective
expect(screen.getByRole('button', { name: 'Create Route' })).toBeInTheDocument()

// Avoid - testing implementation details
expect(wrapper.find('.create-button')).toHaveLength(1)
```

### Common Patterns

#### Testing Form Validation
```typescript
it('validates required fields', async () => {
  render(<RouteCreate />)
  await user.click(screen.getByText('Create'))
  
  expect(screen.getByText('Route title is required')).toBeInTheDocument()
})
```

#### Testing API Errors
```typescript
it('handles API errors gracefully', async () => {
  mockService.getRouteDetails.mockRejectedValue(new Error('API Error'))
  render(<RouteDetails />)
  
  await waitFor(() => {
    expect(mockFire).toHaveBeenCalledWith(
      expect.objectContaining({ icon: 'error' })
    )
  })
})
```

#### Testing Async Operations
```typescript
it('loads data on mount', async () => {
  render(<RouteList />)
  
  await waitFor(() => {
    expect(screen.getByText('Route 1')).toBeInTheDocument()
  })
  
  expect(mockGetRouteList).toHaveBeenCalled()
})
```

## Coverage Requirements

### Minimum Thresholds
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Priority Areas (90%+ Coverage)
- Service layer functions
- Form validation logic
- Error handling
- Critical user workflows

### Excluded from Coverage
- Configuration files (*.config.js)
- Type definitions (*.d.ts)
- Layout components
- Stories files (*.stories.*)

## Debugging Tests

### Common Issues and Solutions

1. **Async Timing Issues**
```typescript
// Use waitFor for async operations
await waitFor(() => {
  expect(screen.getByText('Loaded')).toBeInTheDocument()
})
```

2. **Mock Persistence**
```typescript
// Reset mocks in beforeEach
beforeEach(() => {
  jest.clearAllMocks()
})
```

3. **DOM Queries**
```typescript
// Use screen.debug() to see current DOM
screen.debug()

// Use specific queries
screen.getByRole('button', { name: 'Submit' })
screen.getByLabelText('Route Name')
```

### Debug Tools
- `screen.debug()`: Print current DOM state
- `screen.logTestingPlaygroundURL()`: Get Testing Playground URL
- Jest `--verbose` flag: Detailed test output
- VS Code Jest extension: Inline debugging

## CI/CD Integration

### Pre-commit Hooks
```bash
# Run tests on staged files
npm test -- --findRelatedTests --passWithNoTests
```

### GitHub Actions / CI Pipeline
```bash
# Run full test suite with coverage
npm run test:ci
```

## Future Enhancements

1. **Visual Regression Testing**: Screenshot comparison
2. **Performance Testing**: Component render performance
3. **Accessibility Testing**: Automated a11y checks with jest-axe
4. **API Integration Testing**: Real API endpoint testing

## Troubleshooting

### Common Error Messages

1. **"Cannot find module"**: Install missing dependency
2. **"Test environment not found"**: Install jest-environment-jsdom
3. **"Unexpected token"**: Check Babel configuration
4. **"Module not mocked"**: Add mock in jest.setup.js

### Getting Help

1. Check Jest documentation: https://jestjs.io/docs/
2. React Testing Library docs: https://testing-library.com/docs/react-testing-library/intro/
3. Review existing test files for patterns
4. Use `screen.debug()` to understand DOM structure

## Example Test Files

See the following files for comprehensive examples:
- `__tests__/pages/route/routeDetails.test.tsx` - Complex component testing
- `__tests__/services/route-service.test.ts` - Service layer testing
- `__tests__/integration/route-workflow.test.tsx` - Integration testing
- `__tests__/components/route/RouteCreate.test.tsx` - Form testing
